-- =====================================================
-- 原始查詢分析和逐步優化
-- 先完全理解原始查詢結構，再進行優化
-- =====================================================

-- 原始查詢結構分析：
-- 外層 SELECT：格式化輸出欄位，包含日期轉換和組合欄位
-- 內層 SELECT：連接 UNION ALL 結果與 BTBA 價格表，計算金額
-- UNION ALL：合併三個不同來源的資料
--   1. ASAB + SBAI + ASAA (主要交易資料)
--   2. ASFA + ASHC (特殊交易資料)  
--   3. ASFF + ASHC (外銷資料，單位*12)

-- 讓我們先建立一個完全對應原始查詢的優化版本

WITH 
-- 預先計算日期範圍，避免重複計算
date_range AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) as start_date,
        SYSDATE as end_date
    FROM dual
),

-- 第一步：重建內層的 UNION ALL 結構，確保與原始查詢完全一致
inner_union_data AS (
    -- 分支1：ASAB + SBAI + ASAA
    SELECT 
        SUBSTR(ABDATE, 1, 5) ABYYMM, 
        A.ABVENN, 
        C.AAITEM TRADE_TYPE, 
        A.ABCSTN, 
        A.ABDATE, 
        C.AADATE,
        C.AASTOP, 
        C.AAROUT, 
        A.ABITNO, 
        DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2, 
        A.ABOQTY,
        A.ABTXAT, 
        A.ABAMTE, 
        A.ABRQTY, 
        A.ABRTXA, 
        A.ABRAMT, 
        A.ABPQTY, 
        A.ABORSD,
        DECODE(A.ABTYPE, 'V', 'VM', 'O') ABTYPE, 
        A.ABVENN || A.ABCSTN CUST_NO,
        (ABOQTY + ABPQTY - ABRQTY) QTY
    FROM ASAB A, SBAI B, ASAA C, date_range dr
    WHERE A.ABITNO = B.AIITNO (+) 
      AND A.ABVENN = C.AAVENN 
      AND A.ABCSTN = C.AACSTN 
      AND C.AAITEM IS NOT NULL
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4), 'yyyymmdd') 
          BETWEEN dr.start_date AND dr.end_date
    
    UNION ALL
    
    -- 分支2：ASFA + ASHC  
    SELECT 
        SUBSTR(FADATE, 1, 5) ABYYMM, 
        FAVENN, 
        HCITEM TRADE_TYPE, 
        FACSTN, 
        FADATE, 
        HCCRDT, 
        HCSTDT, 
        HCROUT,
        NULL FAITNO, 
        FAITNO FAITNO2, 
        FASQTY, 
        FAAMTE, 
        0, 
        0, 
        0, 
        0, 
        0, 
        NULL, 
        NULL, 
        FAVENN || FACSTN CUST_NO,
        FASQTY
    FROM ASFA A, ASHC C, date_range dr
    WHERE FAVENN = HCVENN 
      AND FACSTN = HCCSTN 
      AND FAROUT = HCROUT 
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(FADATE, 1, 3)) + 1911) || SUBSTR(FADATE, 4, 4), 'yyyymmdd') 
          BETWEEN dr.start_date AND dr.end_date
    
    UNION ALL
    
    -- 分支3：ASFF + ASHC
    SELECT 
        SUBSTR(FFDATE, 1, 5) ABYYMM, 
        FFVENN, 
        HCITEM TRADE_TYPE, 
        FFVENN FFCSTN, 
        FFDATE, 
        HCCRDT, 
        HCSTDT,
        HCROUT, 
        NULL FFITNO, 
        FFITNO FAITNO2, 
        FFOQTY * 12, -- 將ASFF的單位-打數，*12換算成瓶罐
        0, 
        FFAMTE FFAMTE, 
        0, 
        0, 
        0, 
        0, 
        NULL, 
        'EX', 
        FFVENN CUST_NO, 
        FFOQTY * 12 -- 將ASFF的單位-打數，*12換算成瓶罐
    FROM ASFF A, ASHC C, date_range dr
    WHERE FFVENN = HCVENN 
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(FFDATE, 1, 3)) + 1911) || SUBSTR(FFDATE, 4, 4), 'yyyymmdd') 
          BETWEEN dr.start_date AND dr.end_date
),

-- 第二步：與 BTBA 連接並計算金額 (對應原始查詢的內層 SELECT)
inner_result AS (
    SELECT 
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01', 'yyyymmdd') ABYYMM, 
        C.ABVENN, 
        C.ABCSTN, 
        C.ABDATE, 
        C.ABITNO2 ABITNO, 
        C.ABOQTY, 
        C.ABTXAT, 
        C.ABAMTE,
        C.ABRQTY, 
        C.ABRTXA, 
        C.ABRAMT, 
        C.ABPQTY, 
        C.ABORSD, 
        C.TRADE_TYPE, 
        C.AADATE, 
        C.AASTOP, 
        C.CUST_NO, 
        NULL as col1,
        NULL as col2, 
        NULL as col3, 
        NULL AAKIND,
        NULL AADISP,
        C.AAROUT,
        ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT, 
        B.BAPRIC,
        B.BARATE,
        B.BARAT1
    FROM inner_union_data C, BTBA B, date_range dr
    WHERE C.ABYYMM = B.BAYYMM 
      AND C.ABITNO2 = B.BAITNO 
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 4), 'yyyymmdd') 
          BETWEEN dr.start_date AND dr.end_date
)

-- 第三步：最終輸出 (對應原始查詢的外層 SELECT)
SELECT 
    ABYYMM, 
    ABVENN, 
    ABCSTN,
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2), 'yyyymmdd') ABDATE,
    ABITNO, 
    ABOQTY, 
    ABTXAT, 
    ABAMTE, 
    ABRQTY, 
    ABRTXA, 
    ABRAMT, 
    ABPQTY, 
    ABORSD, 
    TRADE_TYPE,
    NULL AASTOP, 
    CUST_NO, 
    NULL, 
    NULL, 
    NULL, 
    AAKIND,
    AADISP,
    AAROUT,
    AMT,
    TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
    ABVENN || ABCSTN ABVENN_ABCSTN,
    ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
    ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
FROM inner_result;

-- =====================================================
-- 這個版本完全對應原始查詢的邏輯結構
-- 主要優化點：
-- 1. 預先計算日期範圍，避免重複計算
-- 2. 使用 CTE 結構化查詢，提高可讀性
-- 3. 保持與原始查詢完全相同的邏輯和結果
-- =====================================================
