-- =====================================================
-- SQL 查詢重構優化 - 不依賴新索引的效能改善
-- 專注於查詢邏輯優化，避免索引負優化風險
-- =====================================================

-- 問題分析：
-- 1. 成本 173K 主要來自於查詢邏輯問題
-- 2. ROC 日期轉換在 WHERE 條件中造成函數掃描
-- 3. UNION-ALL 結構可能造成重複計算
-- 4. 雜湊連接順序不當導致記憶體浪費

-- =====================================================
-- 第一部分：ROC 日期轉換優化
-- =====================================================

-- 原始問題寫法 (避免使用)：
/*
WHERE TO_DATE((SUBSTR(date_column, 1, 3) + 1911) || SUBSTR(date_column, 4, 4), 'YYYYMMDD') 
      BETWEEN SYSDATE - 1095 AND SYSDATE
*/

-- 優化寫法 1：預先計算日期邊界，使用字串比較
WITH date_boundaries AS (
    SELECT 
        -- 計算 ROC 格式的日期範圍
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE - 1095, 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
)
SELECT ...
FROM your_tables, date_boundaries db
WHERE roc_date_column BETWEEN db.start_roc_date AND db.end_roc_date;

-- 優化寫法 2：使用 CASE 語句避免重複轉換
WITH converted_dates AS (
    SELECT 
        original_columns.*,
        CASE 
            WHEN LENGTH(TRIM(roc_date_column)) = 7 
                 AND SUBSTR(roc_date_column, 1, 3) BETWEEN '001' AND '999'
            THEN TO_DATE(
                (SUBSTR(TRIM(roc_date_column), 1, 3) + 1911) || 
                SUBSTR(TRIM(roc_date_column), 4, 4), 
                'YYYYMMDD'
            )
            ELSE NULL
        END as converted_date
    FROM original_table original_columns
)
SELECT ...
FROM converted_dates
WHERE converted_date BETWEEN SYSDATE - 1095 AND SYSDATE;

-- =====================================================
-- 第二部分：連接順序優化
-- =====================================================

-- 原始問題結構 (大表先連接)：
/*
SELECT ...
FROM BTBA b                    -- 可能是大表
JOIN ASAB ab ON b.key = ab.key -- 大表連接
JOIN ASAA aa ON ab.ref = aa.ref
JOIN ASFA fa ON aa.acc = fa.acc
JOIN ASFF ff ON fa.tid = ff.tid
*/

-- 優化寫法：先過濾再連接，從小表開始
WITH 
-- 步驟1：先過濾主表，減少資料量
filtered_main AS (
    SELECT /*+ FIRST_ROWS(1000) */
        key_column,
        date_column,
        amount_column,
        status_column
    FROM BTBA
    WHERE status_column = 'ACTIVE'  -- 先用簡單條件過濾
      AND date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101'
      AND date_column <= LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || '1231'
),
-- 步驟2：過濾關聯表
filtered_asab AS (
    SELECT reference_column, key_column, active_flag
    FROM ASAB 
    WHERE active_flag = 'Y'
),
filtered_asaa AS (
    SELECT account_column, reference_column, status_code
    FROM ASAA 
    WHERE status_code IN ('APPROVED', 'PROCESSED')
),
-- 步驟3：從最小的結果集開始連接
small_result_set AS (
    SELECT 
        fm.key_column,
        fm.date_column,
        fm.amount_column,
        fab.reference_column
    FROM filtered_main fm
    JOIN filtered_asab fab ON fm.key_column = fab.key_column
    WHERE fm.amount_column > 0  -- 額外過濾條件
),
-- 步驟4：逐步擴展連接
expanded_result AS (
    SELECT 
        srs.*,
        faa.account_column
    FROM small_result_set srs
    JOIN filtered_asaa faa ON srs.reference_column = faa.reference_column
)
-- 最終查詢
SELECT 
    er.key_column,
    er.date_column,
    er.amount_column,
    er.account_column,
    fa.transaction_amount,
    ff.final_status
FROM expanded_result er
LEFT JOIN ASFA fa ON er.account_column = fa.account_column
LEFT JOIN ASFF ff ON fa.transaction_id = ff.transaction_id;

-- =====================================================
-- 第三部分：UNION-ALL 結構優化
-- =====================================================

-- 原始問題結構 (重複掃描)：
/*
SELECT col1, col2, 'TYPE_A' as source FROM table1 WHERE condition1
UNION ALL
SELECT col1, col2, 'TYPE_B' as source FROM table1 WHERE condition2
UNION ALL
SELECT col1, col2, 'TYPE_C' as source FROM table2 WHERE condition3
*/

-- 優化寫法 1：單次掃描 + 條件標記
SELECT 
    col1,
    col2,
    CASE 
        WHEN condition1 THEN 'TYPE_A'
        WHEN condition2 THEN 'TYPE_B'
        ELSE 'OTHER'
    END as source
FROM table1
WHERE condition1 OR condition2
UNION ALL
SELECT col1, col2, 'TYPE_C' as source 
FROM table2 
WHERE condition3;

-- 優化寫法 2：使用 UNPIVOT 避免多次 UNION
WITH base_data AS (
    SELECT 
        key_column,
        date_column,
        type_a_amount,
        type_b_amount,
        type_c_amount
    FROM combined_table
    WHERE date_filter_condition
)
SELECT 
    key_column,
    date_column,
    amount_value,
    amount_type
FROM base_data
UNPIVOT (
    amount_value FOR amount_type IN (
        type_a_amount AS 'TYPE_A',
        type_b_amount AS 'TYPE_B', 
        type_c_amount AS 'TYPE_C'
    )
)
WHERE amount_value IS NOT NULL;

-- =====================================================
-- 第四部分：子查詢優化
-- =====================================================

-- 原始問題：相關子查詢造成 N+1 問題
/*
SELECT 
    main.*,
    (SELECT COUNT(*) FROM detail WHERE detail.main_id = main.id) as detail_count,
    (SELECT SUM(amount) FROM transactions WHERE trans.main_id = main.id) as total_amount
FROM main_table main
*/

-- 優化寫法：使用 LEFT JOIN 替代相關子查詢
WITH aggregated_details AS (
    SELECT 
        main_id,
        COUNT(*) as detail_count,
        SUM(amount) as total_amount
    FROM detail_table
    GROUP BY main_id
),
aggregated_transactions AS (
    SELECT 
        main_id,
        SUM(amount) as transaction_total,
        COUNT(*) as transaction_count
    FROM transactions
    GROUP BY main_id
)
SELECT 
    m.*,
    NVL(ad.detail_count, 0) as detail_count,
    NVL(at.transaction_total, 0) as total_amount,
    NVL(at.transaction_count, 0) as transaction_count
FROM main_table m
LEFT JOIN aggregated_details ad ON m.id = ad.main_id
LEFT JOIN aggregated_transactions at ON m.id = at.main_id;

-- =====================================================
-- 第五部分：記憶體優化的查詢結構
-- =====================================================

-- 針對大型雜湊連接的優化寫法
SELECT /*+ 
    FIRST_ROWS(1000)
    USE_HASH(b ab)
    SWAP_JOIN_INPUTS(ab)
    PQ_DISTRIBUTE(ab HASH HASH)
*/
    b.key_column,
    b.date_column,
    ab.reference_column,
    aa.account_column
FROM (
    -- 先大幅減少主表的資料量
    SELECT key_column, date_column, amount_column
    FROM BTBA 
    WHERE date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101'
      AND amount_column > 1000  -- 假設的業務過濾條件
      AND ROWNUM <= 100000      -- 限制處理的資料量
) b
JOIN (
    -- 同樣減少關聯表的資料量
    SELECT key_column, reference_column
    FROM ASAB 
    WHERE active_flag = 'Y'
      AND created_date >= SYSDATE - 1095
) ab ON b.key_column = ab.key_column
JOIN ASAA aa ON ab.reference_column = aa.reference_column;

-- =====================================================
-- 第六部分：分批處理策略
-- =====================================================

-- 對於超大資料量，使用分批處理
WITH batch_control AS (
    SELECT 
        LEVEL as batch_number,
        (LEVEL - 1) * 10000 + 1 as start_row,
        LEVEL * 10000 as end_row
    FROM dual
    CONNECT BY LEVEL <= 10  -- 分成10批處理
),
numbered_data AS (
    SELECT 
        ROW_NUMBER() OVER (ORDER BY date_column, key_column) as rn,
        key_column,
        date_column,
        amount_column
    FROM BTBA
    WHERE date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101'
)
SELECT 
    bc.batch_number,
    nd.key_column,
    nd.date_column,
    nd.amount_column
FROM batch_control bc
JOIN numbered_data nd ON nd.rn BETWEEN bc.start_row AND bc.end_row
WHERE bc.batch_number = 1;  -- 處理第一批，可以用參數控制

-- =====================================================
-- 第七部分：Power BI 專用的簡化查詢
-- =====================================================

-- 為 Power BI 設計的高效查詢結構
WITH monthly_summary AS (
    SELECT 
        SUBSTR(date_column, 1, 5) as year_month,  -- ROC年月
        account_type,
        status_code,
        COUNT(*) as transaction_count,
        SUM(amount_column) as total_amount,
        AVG(amount_column) as avg_amount
    FROM (
        SELECT 
            b.date_column,
            b.amount_column,
            aa.account_type,
            ff.status_code
        FROM BTBA b
        JOIN ASAB ab ON b.key_column = ab.key_column
        JOIN ASAA aa ON ab.reference_column = aa.reference_column
        JOIN ASFF ff ON aa.account_column = ff.account_column
        WHERE b.date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '01'
          AND ab.active_flag = 'Y'
          AND aa.status_code IN ('APPROVED', 'PROCESSED')
    ) detail_data
    GROUP BY 
        SUBSTR(date_column, 1, 5),
        account_type,
        status_code
)
SELECT 
    year_month,
    account_type,
    status_code,
    transaction_count,
    total_amount,
    avg_amount,
    -- 新增 Power BI 友善的欄位
    (SUBSTR(year_month, 1, 3) + 1911) || '/' || SUBSTR(year_month, 4, 2) as display_date,
    CASE 
        WHEN total_amount > ******** THEN 'High Volume'
        WHEN total_amount > 1000000 THEN 'Medium Volume'
        ELSE 'Low Volume'
    END as volume_category
FROM monthly_summary
ORDER BY year_month DESC, account_type, status_code;

-- =====================================================
-- 第八部分：測試和比較腳本
-- =====================================================

-- 效能測試腳本
SET TIMING ON
SET AUTOTRACE ON STATISTICS

-- 測試原始查詢
-- [在此放入原始查詢]

-- 測試優化後查詢  
-- [在此放入優化後查詢]

SET AUTOTRACE OFF
SET TIMING OFF

-- 比較執行計畫
EXPLAIN PLAN SET STATEMENT_ID = 'ORIGINAL' FOR
[原始查詢];

EXPLAIN PLAN SET STATEMENT_ID = 'OPTIMIZED' FOR  
[優化查詢];

-- 顯示兩個執行計畫的比較
SELECT 
    statement_id,
    operation,
    options,
    object_name,
    cost,
    cardinality,
    bytes
FROM plan_table 
WHERE statement_id IN ('ORIGINAL', 'OPTIMIZED')
ORDER BY statement_id, id;

-- =====================================================
-- 實施建議：
-- 1. 先測試日期轉換優化 (第一部分)
-- 2. 再測試連接順序優化 (第二部分)  
-- 3. 最後實施完整的重構查詢
-- 4. 每步都要比較執行計畫和效能
-- 5. 確認資料正確性後再進行下一步
-- =====================================================
