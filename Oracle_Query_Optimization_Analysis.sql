-- =====================================================
-- Oracle SQL 查詢效能優化分析與建議
-- 針對 Power BI 視圖的效能問題解決方案
-- =====================================================

-- 問題摘要：
-- 1. 查詢成本過高 (173K)
-- 2. 多個全表掃描 (TABLE ACCESS FULL)
-- 3. 大型雜湊連接使用 8160K 臨時空間
-- 4. 涉及 ROC 日期轉換邏輯
-- 5. UNION-ALL 結構效能問題

-- =====================================================
-- 第一部分：索引優化策略
-- =====================================================

-- 1. ROC 日期轉換函數索引
-- 針對民國年轉西元年的日期過濾優化
CREATE INDEX idx_btba_roc_date_conversion ON BTBA (
    TO_DATE(
        CASE 
            WHEN LENGTH(TRIM(date_column)) = 7 THEN
                (SUBSTR(TRIM(date_column), 1, 3) + 1911) || 
                SUBSTR(TRIM(date_column), 4, 4)
            ELSE NULL
        END, 
        'YYYYMMDD'
    )
) WHERE date_column IS NOT NULL;

-- 2. 主要連接鍵的複合索引
CREATE INDEX idx_asab_join_performance ON ASAB (
    primary_key_column,
    foreign_key_column,
    date_filter_column
);

CREATE INDEX idx_asaa_join_performance ON ASAA (
    primary_key_column,
    foreign_key_column,
    status_column
);

CREATE INDEX idx_asfa_join_performance ON ASFA (
    account_key,
    transaction_date,
    amount_column
);

CREATE INDEX idx_asff_join_performance ON ASFF (
    reference_key,
    process_date,
    flag_column
);

-- 3. 覆蓋索引 (Covering Index) 策略
-- 包含查詢所需的所有欄位，避免回表操作
CREATE INDEX idx_btba_covering ON BTBA (
    key_column,
    date_column,
    status_column,
    amount_column,
    description_column
);

-- =====================================================
-- 第二部分：查詢重構建議
-- =====================================================

-- 原始查詢結構優化範例
-- 假設原始查詢類似以下結構：

-- 優化前的問題查詢結構：
/*
SELECT columns
FROM BTBA b
JOIN ASAB ab ON b.key = ab.key
JOIN ASAA aa ON ab.ref = aa.ref
JOIN ASFA fa ON aa.account = fa.account
JOIN ASFF ff ON fa.trans_id = ff.trans_id
WHERE TO_DATE((SUBSTR(b.date_col, 1, 3) + 1911) || SUBSTR(b.date_col, 4, 4), 'YYYYMMDD') 
      BETWEEN SYSDATE - 1095 AND SYSDATE
UNION ALL
SELECT columns
FROM similar_structure_with_different_conditions;
*/

-- 優化後的查詢結構：
WITH 
-- 預先計算日期範圍，避免重複計算
date_boundaries AS (
    SELECT 
        TO_CHAR(SYSDATE - 1095, 'YYYYMMDD') as start_date_std,
        TO_CHAR(SYSDATE, 'YYYYMMDD') as end_date_std,
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE - 1095, 'MMDD') as start_date_roc,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_date_roc
    FROM dual
),
-- 預先過濾主表，減少後續連接的資料量
filtered_btba AS (
    SELECT /*+ INDEX(b idx_btba_roc_date_conversion) */
        b.*
    FROM BTBA b, date_boundaries db
    WHERE b.date_column BETWEEN db.start_date_roc AND db.end_date_roc
      AND b.status_column = 'ACTIVE'  -- 假設有狀態過濾
),
-- 優化連接順序，從最小的結果集開始
optimized_joins AS (
    SELECT /*+ LEADING(fb) USE_HASH(ab aa fa ff) */
        fb.key_column,
        fb.date_column,
        fb.amount_column,
        ab.reference_column,
        aa.account_column,
        fa.transaction_amount,
        ff.final_status,
        'TYPE_A' as record_type
    FROM filtered_btba fb
    JOIN ASAB ab ON fb.key_column = ab.key_column
    JOIN ASAA aa ON ab.reference_column = aa.reference_column
    JOIN ASFA fa ON aa.account_column = fa.account_column
    JOIN ASFF ff ON fa.transaction_id = ff.transaction_id
    WHERE ab.active_flag = 'Y'
      AND aa.status_code IN ('APPROVED', 'PROCESSED')
)
SELECT 
    key_column,
    date_column,
    amount_column,
    reference_column,
    account_column,
    transaction_amount,
    final_status,
    record_type
FROM optimized_joins;

-- =====================================================
-- 第三部分：物化視圖策略 (Power BI 專用)
-- =====================================================

-- 建立快速重新整理的物化視圖
CREATE MATERIALIZED VIEW mv_powerbi_financial_summary
BUILD IMMEDIATE
REFRESH FAST ON DEMAND
ENABLE QUERY REWRITE
AS
SELECT 
    TRUNC(TO_DATE((SUBSTR(b.date_column, 1, 3) + 1911) || 
                  SUBSTR(b.date_column, 4, 4), 'YYYYMMDD'), 'MM') as report_month,
    aa.account_type,
    ff.status_category,
    COUNT(*) as transaction_count,
    SUM(fa.amount) as total_amount,
    AVG(fa.amount) as average_amount,
    MAX(b.date_column) as latest_transaction_date
FROM BTBA b
JOIN ASAB ab ON b.key_column = ab.key_column
JOIN ASAA aa ON ab.reference_column = aa.reference_column
JOIN ASFA fa ON aa.account_column = fa.account_column
JOIN ASFF ff ON fa.transaction_id = ff.transaction_id
WHERE TO_DATE((SUBSTR(b.date_column, 1, 3) + 1911) || 
              SUBSTR(b.date_column, 4, 4), 'YYYYMMDD') >= SYSDATE - 1095
GROUP BY 
    TRUNC(TO_DATE((SUBSTR(b.date_column, 1, 3) + 1911) || 
                  SUBSTR(b.date_column, 4, 4), 'YYYYMMDD'), 'MM'),
    aa.account_type,
    ff.status_category;

-- 為物化視圖建立索引
CREATE INDEX idx_mv_powerbi_month ON mv_powerbi_financial_summary (report_month);
CREATE INDEX idx_mv_powerbi_type ON mv_powerbi_financial_summary (account_type, status_category);

-- =====================================================
-- 第四部分：系統參數調整
-- =====================================================

-- 1. 會話層級的記憶體調整
ALTER SESSION SET pga_aggregate_target = 2G;
ALTER SESSION SET workarea_size_policy = AUTO;
ALTER SESSION SET hash_area_size = 1048576;  -- 1MB
ALTER SESSION SET sort_area_size = 1048576;  -- 1MB

-- 2. 優化器設定
ALTER SESSION SET optimizer_mode = ALL_ROWS;
ALTER SESSION SET optimizer_features_enable = '19.1.0';
ALTER SESSION SET optimizer_index_cost_adj = 10;
ALTER SESSION SET optimizer_index_caching = 90;

-- 3. 平行處理設定 (如果系統資源允許)
ALTER SESSION SET parallel_degree_policy = AUTO;
ALTER SESSION SET parallel_min_time_threshold = 30;

-- =====================================================
-- 第五部分：統計資訊維護
-- =====================================================

-- 定期更新統計資訊的腳本
BEGIN
    -- 收集表格統計資訊
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'BTBA',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(USER, 'ASAB', cascade => TRUE);
    DBMS_STATS.GATHER_TABLE_STATS(USER, 'ASAA', cascade => TRUE);
    DBMS_STATS.GATHER_TABLE_STATS(USER, 'ASFA', cascade => TRUE);
    DBMS_STATS.GATHER_TABLE_STATS(USER, 'ASFF', cascade => TRUE);
    
    -- 收集物化視圖統計資訊
    DBMS_STATS.GATHER_TABLE_STATS(USER, 'MV_POWERBI_FINANCIAL_SUMMARY', cascade => TRUE);
END;
/

-- =====================================================
-- 第六部分：監控與診斷查詢
-- =====================================================

-- 1. 檢查執行計畫
EXPLAIN PLAN FOR
SELECT * FROM mv_powerbi_financial_summary 
WHERE report_month >= TRUNC(SYSDATE - 90, 'MM');

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 2. 監控查詢效能
SELECT 
    sql_id,
    plan_hash_value,
    executions,
    elapsed_time/1000000 as elapsed_seconds,
    cpu_time/1000000 as cpu_seconds,
    buffer_gets,
    disk_reads,
    rows_processed
FROM v$sql 
WHERE sql_text LIKE '%mv_powerbi_financial_summary%'
ORDER BY elapsed_time DESC;

-- 3. 檢查索引使用情況
SELECT 
    i.index_name,
    i.table_name,
    i.uniqueness,
    i.status,
    s.num_rows,
    s.distinct_keys,
    s.clustering_factor
FROM user_indexes i
LEFT JOIN user_ind_statistics s ON i.index_name = s.index_name
WHERE i.table_name IN ('BTBA', 'ASAB', 'ASAA', 'ASFA', 'ASFF')
ORDER BY i.table_name, i.index_name;

-- =====================================================
-- 第七部分：Power BI 連接優化建議
-- =====================================================

-- 建立專用的 Power BI 視圖
CREATE OR REPLACE VIEW vw_powerbi_optimized AS
SELECT 
    report_month,
    account_type,
    status_category,
    transaction_count,
    total_amount,
    average_amount,
    latest_transaction_date,
    -- 新增計算欄位供 Power BI 使用
    CASE 
        WHEN total_amount > 1000000 THEN 'High Value'
        WHEN total_amount > 100000 THEN 'Medium Value'
        ELSE 'Low Value'
    END as amount_category,
    -- 年度和季度欄位
    EXTRACT(YEAR FROM report_month) as report_year,
    'Q' || TO_CHAR(report_month, 'Q') as report_quarter
FROM mv_powerbi_financial_summary
WHERE report_month >= TRUNC(SYSDATE - 1095, 'MM');  -- 最近36個月

-- 授權給 Power BI 使用者
-- GRANT SELECT ON vw_powerbi_optimized TO powerbi_user;

-- =====================================================
-- 實施建議順序：
-- 1. 先建立索引 (第一部分)
-- 2. 更新統計資訊 (第五部分)
-- 3. 測試優化後的查詢 (第二部分)
-- 4. 建立物化視圖 (第三部分)
-- 5. 建立 Power BI 視圖 (第七部分)
-- 6. 設定定期維護作業
-- =====================================================
