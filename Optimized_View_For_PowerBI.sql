-- =====================================================
-- 為 Power BI 優化的 VIEW - 實用版本
-- 平衡效能和靈活性
-- =====================================================

-- 策略：只保留最近12個月的資料在 VIEW 中
-- 這樣可以大幅提升效能，同時保持 Power BI 的靈活性

DROP VIEW ASUSER.FACT_ASAB_AD;

CREATE OR REPLACE FORCE VIEW ASUSER.FACT_ASAB_AD
(ABYYMM, ABVENN, ABCSTN, ABDATE, ABITNO, 
 ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, 
 ABRAMT, ABPQTY, ABORSD, ABTYPE, AASTOP, 
 CUST_NO, PM, BRAND, KIND, AAKIND, 
 AADISP, AAROUT, AMT, YY<PERSON>_ABITNO, ABVENN_ABCSTN, 
 ABVENN_ABCSTN_TYPE, ABVENN_ABCSTN_LARGE)
AS 
SELECT ABYYMM, ABVENN, ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2),
               'yyyymmdd') ABDATE,
       ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
       NULL AASTOP, CUST_NO, NULL PM, NULL BRAND, NULL KIND, AAKIND,
       AADISP,
       AAROUT,
       AMT,
       TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
       ABVENN || ABCSTN ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
FROM (
    SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01',
                   'yyyymmdd') ABYYMM,
           C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
           C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO, NULL,
           NULL, NULL, NULL AAKIND,
           NULL AADISP,
           C.AAROUT,
           ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT,
           B.BAPRIC,
           B.BARATE,
           B.BARAT1
    FROM (
        -- 第一個 UNION：ASAB + SBAI + ASAA (優化：只查詢最近12個月)
        SELECT SUBSTR(A.ABDATE, 1, 5) ABYYMM,
               A.ABVENN,
               C.AAITEM TRADE_TYPE,
               A.ABCSTN,
               A.ABDATE,
               C.AADATE,
               C.AASTOP,
               C.AAROUT,
               A.ABITNO,
               DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2,
               A.ABOQTY,
               A.ABTXAT,
               A.ABAMTE,
               A.ABRQTY,
               A.ABRTXA,
               A.ABRAMT,
               A.ABPQTY,
               A.ABORSD,
               DECODE(A.ABTYPE, 'V', 'VM', 'O') ABTYPE,
               A.ABVENN || A.ABCSTN CUST_NO,
               (A.ABOQTY + A.ABPQTY - A.ABRQTY) QTY
        FROM ASAB A, SBAI B, ASAA C
        WHERE A.ABITNO = B.AIITNO (+)
          AND A.ABVENN = C.AAVENN
          AND A.ABCSTN = C.AACSTN
          AND C.AAITEM IS NOT NULL
          -- 關鍵優化：只查詢最近12個月，大幅減少資料量
          AND SUBSTR(A.ABDATE, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                        TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE

        UNION ALL

        -- 第二個 UNION：ASFA + ASHC (同樣只查詢最近12個月)
        SELECT SUBSTR(A.FADATE, 1, 5) ABYYMM,
               A.FAVENN,
               C.HCITEM TRADE_TYPE,
               A.FACSTN,
               A.FADATE,
               C.HCCRDT,
               C.HCSTDT,
               C.HCROUT,
               NULL FAITNO,
               A.FAITNO FAITNO2,
               A.FASQTY,
               A.FAAMTE,
               0,
               0,
               0,
               0,
               0,
               NULL,
               NULL,
               A.FAVENN || A.FACSTN CUST_NO,
               A.FASQTY
        FROM ASFA A, ASHC C
        WHERE A.FAVENN = C.HCVENN
          AND A.FACSTN = C.HCCSTN
          AND A.FAROUT = C.HCROUT
          -- 關鍵優化：只查詢最近12個月
          AND SUBSTR(A.FADATE, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                        TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FADATE, 1, 3)) + 1911) || SUBSTR(A.FADATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE

        UNION ALL

        -- 第三個 UNION：ASFF + ASHC (同樣只查詢最近12個月)
        SELECT SUBSTR(A.FFDATE, 1, 5) ABYYMM,
               A.FFVENN,
               C.HCITEM TRADE_TYPE,
               A.FFVENN FFCSTN,
               A.FFDATE,
               C.HCCRDT,
               C.HCSTDT,
               C.HCROUT,
               NULL FFITNO,
               A.FFITNO FAITNO2,
               A.FFOQTY * 12,
               0,
               A.FFAMTE FFAMTE,
               0,
               0,
               0,
               0,
               NULL,
               'EX',
               A.FFVENN CUST_NO,
               A.FFOQTY * 12
        FROM ASFF A, ASHC C
        WHERE A.FFVENN = C.HCVENN
          -- 關鍵優化：只查詢最近12個月
          AND SUBSTR(A.FFDATE, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                        TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FFDATE, 1, 3)) + 1911) || SUBSTR(A.FFDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
    ) C,
    (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1 
     FROM BTBA 
     WHERE BAYYMM >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                     TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')) B
    WHERE C.ABYYMM = B.BAYYMM
      AND C.ABITNO2 = B.BAITNO
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(C.ABDATE, 1, 3)) + 1911) || SUBSTR(C.ABDATE, 4, 4),
                  'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
);

-- =====================================================
-- 為 VIEW 建立建議的索引 (如果底層表格沒有的話)
-- =====================================================

-- 檢查並建立必要的索引
-- CREATE INDEX idx_asab_date_venn ON ASAB (SUBSTR(ABDATE, 1, 5), ABVENN);
-- CREATE INDEX idx_asfa_date_venn ON ASFA (SUBSTR(FADATE, 1, 5), FAVENN);
-- CREATE INDEX idx_asff_date_venn ON ASFF (SUBSTR(FFDATE, 1, 5), FFVENN);
-- CREATE INDEX idx_btba_bayymm ON BTBA (BAYYMM);

-- =====================================================
-- 使用說明：
-- 
-- 1. 這個 VIEW 只包含最近12個月的資料
-- 2. Power BI 可以在此基礎上進一步過濾
-- 3. 查詢效能會比原始的36個月資料快很多
-- 4. 如果需要更長的歷史資料，可以調整月份數
-- 
-- 5. Power BI 使用範例：
--    SELECT * FROM ASUSER.FACT_ASAB_AD 
--    WHERE ABVENN = '32050' 
--      AND TO_CHAR(ABYYMM, 'YYYYMM') = '202406'
-- 
-- 6. 效能預期：
--    - 原始查詢：處理36個月資料 → 很慢
--    - 優化後：只處理12個月資料 → 快很多
--    - Power BI 再過濾：在較小的資料集上過濾 → 很快
-- =====================================================
