-- =====================================================
-- 月彙總查詢 - 已加上年月和經銷商條件
-- 年月：11406 (民國114年6月)
-- 經銷商：32050
-- =====================================================

SELECT  abyymm,
        abvenn,
        abcstn,
        abdate,
        abitno,
        sum(aboqty) aboqty,
        sum(abtxat) abtxat,
        sum(abamte) abamte,
        sum(abrqty) abrqty,
        sum(abrtxa) abrtxa,
        sum(abramt) abramt,
        sum(abpqty) abpqty,
        aborsd,
        trade_type,
        aastop,    
        CUST_NO, 
        PM, 
        BRAND, 
        KIND, 
        aakind,
        aadisp,
        aarout,
        sum(amt) amt,
        yymm_abitno,
        abvenn_abcstn,
        abvenn_abcstn_type,
        ABVENN_ABCSTN_LARGE
FROM (
    SELECT abyymm,
           abvenn,
           abcstn,
           TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 2) || '01', 'yyyymmdd') abdate,
           abitno,
           aboqty,
           abtxat,
           abamte,
           abrqty,
           abrtxa,
           abramt,
           abpqty,
           aborsd,
           trade_type,
           NULL aastop,    
           cust_no,
           NULL PM,
           NULL BRAND,
           NULL KIND,
           aakind,
           aadisp,
           aarout,
           amt,
           TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno,
           abvenn||abcstn abvenn_abcstn,
           abvenn||abcstn||trade_type abvenn_abcstn_type,
           abvenn||abcstn||SUBSTR(aarout, 1, 1) ABVENN_ABCSTN_LARGE
    FROM (
        SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd') abyymm,
               c.abvenn,
               c.abcstn,
               c.abdate,
               c.abitno2 abitno,
               c.aboqty,
               c.abtxat,
               c.abamte,
               c.abrqty,
               c.abrtxa,
               c.abramt,
               c.abpqty,
               c.aborsd,
               c.trade_type,
               c.aadate,
               c.aastop,
               c.cust_no,
               NULL,
               NULL,
               NULL,
               NULL aakind,
               NULL aadisp,
               c.aarout,
               ROUND((ROUND((qty * bapric) / barate, 0) * (1 - barat1)), 0) amt,
               b.bapric,
               b.barate,
               b.barat1
        FROM (
            -- 第一個 UNION：ASAB + SBAI + ASAA (在最內層過濾)
            SELECT SUBSTR(a.abdate, 1, 5) abyymm,
                   a.abvenn,
                   c.aaitem trade_type,
                   a.abcstn,
                   a.abdate,
                   c.aadate,
                   c.aastop,
                   c.aarout,
                   a.abitno,
                   DECODE(a.abitno, b.aiitno, b.aiitno2, a.abitno) abitno2,
                   a.aboqty,
                   a.abtxat,
                   a.abamte,
                   a.abrqty,
                   a.abrtxa,
                   a.abramt,
                   a.abpqty,
                   a.aborsd,
                   DECODE(a.abtype, 'V', 'VM', 'O') abtype,
                   a.abvenn || a.abcstn cust_no,
                   (a.aboqty + a.abpqty - a.abrqty) qty
            FROM asab a, sbai b, asaa c
            WHERE a.abitno = b.aiitno(+)
              AND a.abvenn = c.aavenn
              AND a.abcstn = c.aacstn
              AND c.aaitem IS NOT NULL
              -- 關鍵優化：在最內層就過濾
              AND SUBSTR(a.abdate, 1, 5) = '11406'  -- 年月過濾
              AND a.abvenn = '32050'                -- 經銷商過濾
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第二個 UNION：ASFA + ASHC (在最內層過濾)
            SELECT SUBSTR(a.fadate, 1, 5) abyymm,
                   a.favenn,
                   c.hcitem trade_type,
                   a.facstn,
                   a.fadate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL faitno,
                   a.faitno faitno2,
                   a.fasqty,
                   a.faamte,
                   0,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   NULL,
                   a.favenn || a.facstn cust_no,
                   a.fasqty
            FROM asfa a, ashc c
            WHERE a.favenn = c.hcvenn
              AND a.facstn = c.hccstn
              AND a.farout = c.hcrout
              -- 關鍵優化：在最內層就過濾
              AND SUBSTR(a.fadate, 1, 5) = '11406'  -- 年月過濾
              AND a.favenn = '32050'                -- 經銷商過濾
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.fadate, 1, 3)) + 1911) || SUBSTR(a.fadate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第三個 UNION：ASFF + ASHC (在最內層過濾)
            SELECT SUBSTR(a.ffdate, 1, 5) abyymm,
                   a.ffvenn,
                   c.hcitem trade_type,
                   a.ffvenn ffcstn,
                   a.ffdate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL ffitno,
                   a.ffitno faitno2,
                   a.ffoqty * 12, -- 將ASFF的單位-打數，*12換算成瓶罐
                   0,
                   a.ffamte ffamte,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   'EX',
                   a.ffvenn cust_no,
                   a.ffoqty * 12 -- 將ASFF的單位-打數，*12換算成瓶罐
            FROM ASFF a, ashc c
            WHERE a.ffvenn = c.hcvenn
              -- 關鍵優化：在最內層就過濾
              AND SUBSTR(a.ffdate, 1, 5) = '11406'  -- 年月過濾
              AND a.ffvenn = '32050'                -- 經銷商過濾
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.ffdate, 1, 3)) + 1911) || SUBSTR(a.ffdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
        ) c,
        (SELECT bayymm, baitno, bapric, barate, barat1 
         FROM btba 
         WHERE bayymm = '11406') b  -- 在 BTBA 表上也加上年月過濾
        WHERE c.abyymm = b.bayymm
          AND c.abitno2 = b.baitno
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.abdate, 1, 3)) + 1911) || SUBSTR(c.abdate, 4, 4), 'yyyymmdd') 
              BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    )
)
GROUP BY abyymm,
         abvenn,
         abcstn,
         abdate,
         abitno,
         aborsd,
         trade_type,
         aastop,    
         CUST_NO, 
         PM, 
         BRAND, 
         KIND, 
         aakind,
         aadisp,
         aarout,
         yymm_abitno,
         abvenn_abcstn,
         abvenn_abcstn_type,
         ABVENN_ABCSTN_LARGE;

-- =====================================================
-- 優化重點：
-- 1. 在每個 UNION 分支的最內層就加上過濾條件
-- 2. 使用字串比較：SUBSTR(a.abdate, 1, 5) = '11406'
-- 3. 在 BTBA 表上也加上過濾：WHERE bayymm = '11406'
-- 4. 避免在外層才過濾，減少不必要的資料處理
-- 
-- 預期效果：
-- - 大幅減少需要處理的資料量
-- - 提高查詢執行速度
-- - 減少記憶體使用
-- =====================================================
