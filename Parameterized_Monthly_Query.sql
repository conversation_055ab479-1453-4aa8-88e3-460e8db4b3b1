-- =====================================================
-- 參數化的月彙總查詢 - 方便修改查詢條件
-- =====================================================

-- 使用方式：
-- 1. 修改下面 WITH 子句中的參數
-- 2. 執行整個查詢

WITH 
-- 查詢參數設定區 (修改這裡的值)
query_params AS (
    SELECT 
        '11406' as target_month,        -- 目標年月 (ROC格式，如：11406 = 民國114年6月)
        '32050' as target_vendor,       -- 目標經銷商代號 (設為 NULL 表示查詢所有經銷商)
        'SINGLE' as month_mode          -- 'SINGLE' = 單月, 'RANGE' = 範圍, 'MULTIPLE' = 多月
        -- 如果是 RANGE 模式，可以在下面設定起始和結束月份
        -- 如果是 MULTIPLE 模式，可以在 WHERE 條件中使用 IN
    FROM dual
)

SELECT  abyymm,
        abvenn,
        abcstn,
        abdate,
        abitno,
        sum(aboqty) aboqty,
        sum(abtxat) abtxat,
        sum(abamte) abamte,
        sum(abrqty) abrqty,
        sum(abrtxa) abrtxa,
        sum(abramt) abramt,
        sum(abpqty) abpqty,
        aborsd,
        trade_type,
        aastop,    
        CUST_NO, 
        PM, 
        BRAND, 
        KIND, 
        aakind,
        aadisp,
        aarout,
        sum(amt) amt,
        yymm_abitno,
        abvenn_abcstn,
        abvenn_abcstn_type,
        ABVENN_ABCSTN_LARGE
FROM (
    SELECT abyymm,
           abvenn,
           abcstn,
           TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 2) || '01', 'yyyymmdd') abdate,
           abitno,
           aboqty,
           abtxat,
           abamte,
           abrqty,
           abrtxa,
           abramt,
           abpqty,
           aborsd,
           trade_type,
           NULL aastop,    
           cust_no,
           NULL PM,
           NULL BRAND,
           NULL KIND,
           aakind,
           aadisp,
           aarout,
           amt,
           TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno,
           abvenn||abcstn abvenn_abcstn,
           abvenn||abcstn||trade_type abvenn_abcstn_type,
           abvenn||abcstn||SUBSTR(aarout, 1, 1) ABVENN_ABCSTN_LARGE
    FROM (
        SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd') abyymm,
               c.abvenn,
               c.abcstn,
               c.abdate,
               c.abitno2 abitno,
               c.aboqty,
               c.abtxat,
               c.abamte,
               c.abrqty,
               c.abrtxa,
               c.abramt,
               c.abpqty,
               c.aborsd,
               c.trade_type,
               c.aadate,
               c.aastop,
               c.cust_no,
               NULL,
               NULL,
               NULL,
               NULL aakind,
               NULL aadisp,
               c.aarout,
               ROUND((ROUND((qty * bapric) / barate, 0) * (1 - barat1)), 0) amt,
               b.bapric,
               b.barate,
               b.barat1
        FROM (
            -- 第一個 UNION：ASAB + SBAI + ASAA
            SELECT SUBSTR(a.abdate, 1, 5) abyymm,
                   a.abvenn,
                   c.aaitem trade_type,
                   a.abcstn,
                   a.abdate,
                   c.aadate,
                   c.aastop,
                   c.aarout,
                   a.abitno,
                   DECODE(a.abitno, b.aiitno, b.aiitno2, a.abitno) abitno2,
                   a.aboqty,
                   a.abtxat,
                   a.abamte,
                   a.abrqty,
                   a.abrtxa,
                   a.abramt,
                   a.abpqty,
                   a.aborsd,
                   DECODE(a.abtype, 'V', 'VM', 'O') abtype,
                   a.abvenn || a.abcstn cust_no,
                   (a.aboqty + a.abpqty - a.abrqty) qty
            FROM asab a, sbai b, asaa c, query_params qp
            WHERE a.abitno = b.aiitno(+)
              AND a.abvenn = c.aavenn
              AND a.abcstn = c.aacstn
              AND c.aaitem IS NOT NULL
              -- 動態年月過濾
              AND SUBSTR(a.abdate, 1, 5) = qp.target_month
              -- 動態經銷商過濾 (如果 target_vendor 不是 NULL)
              AND (qp.target_vendor IS NULL OR a.abvenn = qp.target_vendor)
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第二個 UNION：ASFA + ASHC
            SELECT SUBSTR(a.fadate, 1, 5) abyymm,
                   a.favenn,
                   c.hcitem trade_type,
                   a.facstn,
                   a.fadate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL faitno,
                   a.faitno faitno2,
                   a.fasqty,
                   a.faamte,
                   0,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   NULL,
                   a.favenn || a.facstn cust_no,
                   a.fasqty
            FROM asfa a, ashc c, query_params qp
            WHERE a.favenn = c.hcvenn
              AND a.facstn = c.hccstn
              AND a.farout = c.hcrout
              -- 動態年月過濾
              AND SUBSTR(a.fadate, 1, 5) = qp.target_month
              -- 動態經銷商過濾 (如果 target_vendor 不是 NULL)
              AND (qp.target_vendor IS NULL OR a.favenn = qp.target_vendor)
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.fadate, 1, 3)) + 1911) || SUBSTR(a.fadate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第三個 UNION：ASFF + ASHC
            SELECT SUBSTR(a.ffdate, 1, 5) abyymm,
                   a.ffvenn,
                   c.hcitem trade_type,
                   a.ffvenn ffcstn,
                   a.ffdate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL ffitno,
                   a.ffitno faitno2,
                   a.ffoqty * 12,
                   0,
                   a.ffamte ffamte,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   'EX',
                   a.ffvenn cust_no,
                   a.ffoqty * 12
            FROM ASFF a, ashc c, query_params qp
            WHERE a.ffvenn = c.hcvenn
              -- 動態年月過濾
              AND SUBSTR(a.ffdate, 1, 5) = qp.target_month
              -- 動態經銷商過濾 (如果 target_vendor 不是 NULL)
              AND (qp.target_vendor IS NULL OR a.ffvenn = qp.target_vendor)
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.ffdate, 1, 3)) + 1911) || SUBSTR(a.ffdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
        ) c,
        (SELECT bayymm, baitno, bapric, barate, barat1 
         FROM btba b, query_params qp
         WHERE b.bayymm = qp.target_month) b
        WHERE c.abyymm = b.bayymm
          AND c.abitno2 = b.baitno
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.abdate, 1, 3)) + 1911) || SUBSTR(c.abdate, 4, 4), 'yyyymmdd') 
              BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    )
)
GROUP BY abyymm,
         abvenn,
         abcstn,
         abdate,
         abitno,
         aborsd,
         trade_type,
         aastop,    
         CUST_NO, 
         PM, 
         BRAND, 
         KIND, 
         aakind,
         aadisp,
         aarout,
         yymm_abitno,
         abvenn_abcstn,
         abvenn_abcstn_type,
         ABVENN_ABCSTN_LARGE;

-- =====================================================
-- 使用範例：
-- 
-- 1. 查詢特定月份的特定經銷商：
--    target_month = '11406', target_vendor = '32050'
-- 
-- 2. 查詢特定月份的所有經銷商：
--    target_month = '11406', target_vendor = NULL
-- 
-- 3. 查詢多個月份 (需要修改 WHERE 條件)：
--    將 = qp.target_month 改為 IN ('11406', '11407', '11408')
-- 
-- 4. 查詢月份範圍 (需要修改 WHERE 條件)：
--    將 = qp.target_month 改為 BETWEEN '11406' AND '11408'
-- =====================================================
