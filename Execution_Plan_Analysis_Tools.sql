-- =====================================================
-- Oracle 執行計畫分析工具與監控腳本
-- 專門針對高成本查詢的診斷和優化
-- =====================================================

-- =====================================================
-- 第一部分：執行計畫詳細分析
-- =====================================================

-- 1. 獲取詳細的執行計畫 (包含成本分析)
SET LINESIZE 200
SET PAGESIZE 1000
COLUMN plan_table_output FORMAT A180

-- 顯示當前查詢的執行計畫
SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY_CURSOR(NULL, NULL, 'ALLSTATS LAST'));

-- 2. 分析高成本操作的腳本
WITH execution_plan_analysis AS (
    SELECT 
        p.id,
        p.operation,
        p.options,
        p.object_name,
        p.cost,
        p.cardinality,
        p.bytes,
        p.cpu_cost,
        p.io_cost,
        ROUND(p.cost / NULLIF(SUM(p.cost) OVER(), 0) * 100, 2) as cost_percentage
    FROM v$sql_plan p
    WHERE p.sql_id = '&sql_id'  -- 替換為實際的 SQL_ID
)
SELECT 
    id,
    LPAD(' ', 2 * LEVEL) || operation || ' ' || options as execution_step,
    object_name,
    cost,
    cost_percentage || '%' as cost_pct,
    cardinality,
    ROUND(bytes/1024/1024, 2) as mb_processed
FROM execution_plan_analysis
CONNECT BY PRIOR id = parent_id
START WITH id = 0
ORDER SIBLINGS BY id;

-- =====================================================
-- 第二部分：問題識別查詢
-- =====================================================

-- 1. 識別全表掃描操作
SELECT 
    s.sql_id,
    s.plan_hash_value,
    p.operation,
    p.options,
    p.object_name,
    p.cost,
    p.cardinality,
    s.executions,
    ROUND(s.elapsed_time/1000000/NULLIF(s.executions,0), 2) as avg_elapsed_sec
FROM v$sql s
JOIN v$sql_plan p ON s.sql_id = p.sql_id AND s.plan_hash_value = p.plan_hash_value
WHERE p.operation = 'TABLE ACCESS'
  AND p.options = 'FULL'
  AND p.object_name IN ('BTBA', 'ASAB', 'ASAA', 'ASFA', 'ASFF')
  AND s.executions > 0
ORDER BY p.cost DESC, s.elapsed_time DESC;

-- 2. 分析雜湊連接的記憶體使用
SELECT 
    s.sql_id,
    s.plan_hash_value,
    p.operation,
    p.options,
    p.cost,
    p.cardinality,
    ROUND(p.bytes/1024/1024, 2) as estimated_mb,
    ws.operation_type,
    ws.work_area_size/1024/1024 as actual_mb_used,
    ws.max_tempseg_size/1024/1024 as max_temp_mb,
    CASE 
        WHEN ws.optimal_executions > 0 THEN 'OPTIMAL'
        WHEN ws.onepass_executions > 0 THEN 'ONE_PASS'
        WHEN ws.multipasses_executions > 0 THEN 'MULTI_PASS'
        ELSE 'UNKNOWN'
    END as execution_mode
FROM v$sql s
JOIN v$sql_plan p ON s.sql_id = p.sql_id AND s.plan_hash_value = p.plan_hash_value
LEFT JOIN v$sql_workarea w ON s.sql_id = w.sql_id AND s.plan_hash_value = w.plan_hash_value
LEFT JOIN v$sqlarea_workarea ws ON w.workarea_address = ws.workarea_address
WHERE p.operation = 'HASH JOIN'
  AND s.executions > 0
ORDER BY ws.max_tempseg_size DESC NULLS LAST;

-- =====================================================
-- 第三部分：索引效能分析
-- =====================================================

-- 1. 檢查索引的選擇性和使用情況
SELECT 
    i.table_name,
    i.index_name,
    i.index_type,
    i.uniqueness,
    ic.column_position,
    ic.column_name,
    ROUND(s.num_rows / NULLIF(s.distinct_keys, 0), 2) as avg_rows_per_key,
    s.clustering_factor,
    CASE 
        WHEN s.clustering_factor < s.num_rows THEN 'GOOD'
        WHEN s.clustering_factor < s.num_rows * 2 THEN 'FAIR'
        ELSE 'POOR'
    END as clustering_quality,
    iu.total_access_count,
    iu.total_exec_count
FROM user_indexes i
JOIN user_ind_columns ic ON i.index_name = ic.index_name
LEFT JOIN user_ind_statistics s ON i.index_name = s.index_name
LEFT JOIN (
    SELECT 
        object_name,
        SUM(access_count) as total_access_count,
        SUM(executions) as total_exec_count
    FROM v$segment_statistics 
    WHERE statistic_name = 'logical reads'
    GROUP BY object_name
) iu ON i.index_name = iu.object_name
WHERE i.table_name IN ('BTBA', 'ASAB', 'ASAA', 'ASFA', 'ASFF')
ORDER BY i.table_name, i.index_name, ic.column_position;

-- 2. 識別未使用的索引
SELECT 
    i.table_name,
    i.index_name,
    i.index_type,
    i.status,
    ROUND(s.num_rows / 1000, 1) as table_rows_k,
    NVL(iu.total_access_count, 0) as access_count,
    CASE 
        WHEN NVL(iu.total_access_count, 0) = 0 THEN 'NEVER_USED'
        WHEN NVL(iu.total_access_count, 0) < 10 THEN 'RARELY_USED'
        ELSE 'ACTIVELY_USED'
    END as usage_status
FROM user_indexes i
LEFT JOIN user_ind_statistics s ON i.index_name = s.index_name
LEFT JOIN (
    SELECT 
        object_name,
        SUM(access_count) as total_access_count
    FROM v$segment_statistics 
    WHERE statistic_name = 'logical reads'
    GROUP BY object_name
) iu ON i.index_name = iu.object_name
WHERE i.table_name IN ('BTBA', 'ASAB', 'ASAA', 'ASFA', 'ASFF')
  AND i.index_type != 'LOB'
ORDER BY iu.total_access_count NULLS FIRST, i.table_name;

-- =====================================================
-- 第四部分：ROC 日期轉換效能分析
-- =====================================================

-- 1. 分析日期轉換函數的效能影響
SELECT 
    table_name,
    column_name,
    data_type,
    num_distinct,
    num_nulls,
    sample_size,
    last_analyzed,
    -- 檢查是否有函數索引
    (SELECT COUNT(*) 
     FROM user_ind_expressions ie
     JOIN user_indexes ui ON ie.index_name = ui.index_name
     WHERE ui.table_name = tc.table_name
       AND UPPER(ie.column_expression) LIKE '%' || UPPER(tc.column_name) || '%'
       AND UPPER(ie.column_expression) LIKE '%1911%'
    ) as has_function_index
FROM user_tab_columns tc
WHERE table_name IN ('BTBA', 'ASAB', 'ASAA', 'ASFA', 'ASFF')
  AND (data_type LIKE '%CHAR%' OR data_type = 'NUMBER')
  AND column_name LIKE '%DATE%'
ORDER BY table_name, column_name;

-- 2. 測試日期轉換效能
-- 建立測試腳本來比較不同日期過濾方法的效能
SET TIMING ON
SET AUTOTRACE ON

-- 方法1：使用函數轉換 (較慢)
SELECT COUNT(*)
FROM BTBA
WHERE TO_DATE((SUBSTR(date_column, 1, 3) + 1911) || SUBSTR(date_column, 4, 4), 'YYYYMMDD') 
      BETWEEN SYSDATE - 1095 AND SYSDATE;

-- 方法2：使用字串比較 (較快，如果有適當索引)
SELECT COUNT(*)
FROM BTBA
WHERE date_column BETWEEN 
    LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || TO_CHAR(SYSDATE - 1095, 'MMDD')
    AND 
    LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || TO_CHAR(SYSDATE, 'MMDD');

SET AUTOTRACE OFF
SET TIMING OFF

-- =====================================================
-- 第五部分：記憶體和臨時空間監控
-- =====================================================

-- 1. 監控 PGA 記憶體使用情況
SELECT 
    s.sid,
    s.serial#,
    s.username,
    s.program,
    ROUND(p.pga_used_mem/1024/1024, 2) as pga_used_mb,
    ROUND(p.pga_alloc_mem/1024/1024, 2) as pga_alloc_mb,
    ROUND(p.pga_max_mem/1024/1024, 2) as pga_max_mb
FROM v$session s
JOIN v$process p ON s.paddr = p.addr
WHERE s.username IS NOT NULL
  AND p.pga_used_mem > 100*1024*1024  -- 大於 100MB
ORDER BY p.pga_used_mem DESC;

-- 2. 監控臨時表空間使用情況
SELECT 
    s.sid,
    s.serial#,
    s.username,
    s.sql_id,
    ROUND(u.blocks * t.block_size / 1024 / 1024, 2) as temp_mb_used,
    u.tablespace,
    sq.sql_text
FROM v$session s
JOIN v$tempseg_usage u ON s.saddr = u.session_addr
JOIN dba_tablespaces t ON u.tablespace = t.tablespace_name
LEFT JOIN v$sql sq ON s.sql_id = sq.sql_id
WHERE u.blocks > 0
ORDER BY u.blocks DESC;

-- =====================================================
-- 第六部分：自動化監控腳本
-- =====================================================

-- 建立監控視圖，用於持續追蹤查詢效能
CREATE OR REPLACE VIEW vw_query_performance_monitor AS
SELECT 
    s.sql_id,
    s.plan_hash_value,
    s.executions,
    ROUND(s.elapsed_time/1000000, 2) as total_elapsed_sec,
    ROUND(s.elapsed_time/1000000/NULLIF(s.executions,0), 3) as avg_elapsed_sec,
    ROUND(s.cpu_time/1000000, 2) as total_cpu_sec,
    ROUND(s.buffer_gets/NULLIF(s.executions,0), 0) as avg_buffer_gets,
    ROUND(s.disk_reads/NULLIF(s.executions,0), 0) as avg_disk_reads,
    s.rows_processed,
    s.first_load_time,
    s.last_active_time,
    SUBSTR(s.sql_text, 1, 100) as sql_text_preview
FROM v$sql s
WHERE s.executions > 0
  AND s.elapsed_time > 1000000  -- 超過1秒的查詢
  AND (UPPER(s.sql_text) LIKE '%BTBA%' 
       OR UPPER(s.sql_text) LIKE '%ASAB%'
       OR UPPER(s.sql_text) LIKE '%ASAA%'
       OR UPPER(s.sql_text) LIKE '%ASFA%'
       OR UPPER(s.sql_text) LIKE '%ASFF%')
ORDER BY s.elapsed_time DESC;

-- 查詢最近的效能問題
SELECT * FROM vw_query_performance_monitor
WHERE last_active_time >= SYSDATE - 1  -- 最近24小時
ORDER BY avg_elapsed_sec DESC;

-- =====================================================
-- 使用說明：
-- 1. 替換 &sql_id 為實際的 SQL ID
-- 2. 定期執行監控查詢以追蹤效能趨勢
-- 3. 根據分析結果調整索引策略
-- 4. 監控記憶體使用情況，適時調整 PGA 設定
-- =====================================================
