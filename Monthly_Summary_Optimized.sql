-- =====================================================
-- 月彙總查詢優化版本
-- 應用相同的優化策略：在最內層就過濾條件
-- =====================================================

-- 如果您要查詢特定年月和經銷商，請修改以下參數：
-- 將 '11406' 改為您要的年月
-- 將 '32050' 改為您要的經銷商代號

SELECT  abyymm,
        abvenn,
        abcstn,
        abdate,
        abitno,
        sum(aboqty) aboqty,
        sum(abtxat) abtxat,
        sum(abamte) abamte,
        sum(abrqty) abrqty,
        sum(abrtxa) abrtxa,
        sum(abramt) abramt,
        sum(abpqty) abpqty,
        aborsd,
        trade_type,
        aastop,    
        CUST_NO, 
        PM, 
        BRAND, 
        KIND, 
        aakind,
        aadisp,
        aarout,
        sum(amt) amt,
        yymm_abitno,
        abvenn_abcstn,
        abvenn_abcstn_type,
        ABVENN_ABCSTN_LARGE
FROM (
    SELECT abyymm,
           abvenn,
           abcstn,
           TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 2) || '01', 'yyyymmdd') abdate,
           abitno,
           aboqty,
           abtxat,
           abamte,
           abrqty,
           abrtxa,
           abramt,
           abpqty,
           aborsd,
           trade_type,
           NULL aastop,    
           cust_no,
           NULL PM,
           NULL BRAND,
           NULL KIND,
           aakind,
           aadisp,
           aarout,
           amt,
           TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno,
           abvenn||abcstn abvenn_abcstn,
           abvenn||abcstn||trade_type abvenn_abcstn_type,
           abvenn||abcstn||SUBSTR(aarout, 1, 1) ABVENN_ABCSTN_LARGE
    FROM (
        SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd') abyymm,
               c.abvenn,
               c.abcstn,
               c.abdate,
               c.abitno2 abitno,
               c.aboqty,
               c.abtxat,
               c.abamte,
               c.abrqty,
               c.abrtxa,
               c.abramt,
               c.abpqty,
               c.aborsd,
               c.trade_type,
               c.aadate,
               c.aastop,
               c.cust_no,
               NULL,
               NULL,
               NULL,
               NULL aakind,
               NULL aadisp,
               c.aarout,
               ROUND((ROUND((qty * bapric) / barate, 0) * (1 - barat1)), 0) amt,
               b.bapric,
               b.barate,
               b.barat1
        FROM (
            -- 第一個 UNION：ASAB + SBAI + ASAA (在這裡加上過濾條件)
            SELECT SUBSTR(a.abdate, 1, 5) abyymm,
                   a.abvenn,
                   c.aaitem trade_type,
                   a.abcstn,
                   a.abdate,
                   c.aadate,
                   c.aastop,
                   c.aarout,
                   a.abitno,
                   DECODE(a.abitno, b.aiitno, b.aiitno2, a.abitno) abitno2,
                   a.aboqty,
                   a.abtxat,
                   a.abamte,
                   a.abrqty,
                   a.abrtxa,
                   a.abramt,
                   a.abpqty,
                   a.aborsd,
                   DECODE(a.abtype, 'V', 'VM', 'O') abtype,
                   a.abvenn || a.abcstn cust_no,
                   (a.aboqty + a.abpqty - a.abrqty) qty
            FROM asab a, sbai b, asaa c
            WHERE a.abitno = b.aiitno(+)
              AND a.abvenn = c.aavenn
              AND a.abcstn = c.aacstn
              AND c.aaitem IS NOT NULL
              -- 關鍵優化：在最內層就過濾特定年月和經銷商
              AND SUBSTR(a.abdate, 1, 5) = '11406'  -- 修改這裡的年月
              AND a.abvenn = '32050'                -- 修改這裡的經銷商代號
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第二個 UNION：ASFA + ASHC (同樣在內層過濾)
            SELECT SUBSTR(a.fadate, 1, 5) abyymm,
                   a.favenn,
                   c.hcitem trade_type,
                   a.facstn,
                   a.fadate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL faitno,
                   a.faitno faitno2,
                   a.fasqty,
                   a.faamte,
                   0,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   NULL,
                   a.favenn || a.facstn cust_no,
                   a.fasqty
            FROM asfa a, ashc c
            WHERE a.favenn = c.hcvenn
              AND a.facstn = c.hccstn
              AND a.farout = c.hcrout
              -- 關鍵優化：在最內層就過濾特定年月和經銷商
              AND SUBSTR(a.fadate, 1, 5) = '11406'  -- 修改這裡的年月
              AND a.favenn = '32050'                -- 修改這裡的經銷商代號
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.fadate, 1, 3)) + 1911) || SUBSTR(a.fadate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
            
            UNION ALL
            
            -- 第三個 UNION：ASFF + ASHC (同樣在內層過濾)
            SELECT SUBSTR(a.ffdate, 1, 5) abyymm,
                   a.ffvenn,
                   c.hcitem trade_type,
                   a.ffvenn ffcstn,
                   a.ffdate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL ffitno,
                   a.ffitno faitno2,
                   a.ffoqty * 12, -- 將ASFF的單位-打數，*12換算成瓶罐
                   0,
                   a.ffamte ffamte,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   'EX',
                   a.ffvenn cust_no,
                   a.ffoqty * 12 -- 將ASFF的單位-打數，*12換算成瓶罐
            FROM ASFF a, ashc c
            WHERE a.ffvenn = c.hcvenn
              -- 關鍵優化：在最內層就過濾特定年月和經銷商
              AND SUBSTR(a.ffdate, 1, 5) = '11406'  -- 修改這裡的年月
              AND a.ffvenn = '32050'                -- 修改這裡的經銷商代號
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.ffdate, 1, 3)) + 1911) || SUBSTR(a.ffdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
        ) c,
        (SELECT bayymm, baitno, bapric, barate, barat1 
         FROM btba 
         WHERE bayymm = '11406') b  -- 也在 BTBA 表上加上過濾
        WHERE c.abyymm = b.bayymm
          AND c.abitno2 = b.baitno
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.abdate, 1, 3)) + 1911) || SUBSTR(c.abdate, 4, 4), 'yyyymmdd') 
              BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    )
)
GROUP BY abyymm,
         abvenn,
         abcstn,
         abdate,
         abitno,
         aborsd,
         trade_type,
         aastop,    
         CUST_NO, 
         PM, 
         BRAND, 
         KIND, 
         aakind,
         aadisp,
         aarout,
         yymm_abitno,
         abvenn_abcstn,
         abvenn_abcstn_type,
         ABVENN_ABCSTN_LARGE;

-- =====================================================
-- 如果您要查詢多個月份，可以使用 IN 條件：
-- 
-- 將：AND SUBSTR(a.abdate, 1, 5) = '11406'
-- 改為：AND SUBSTR(a.abdate, 1, 5) IN ('11406', '11407', '11408')
-- 
-- 將：WHERE bayymm = '11406'
-- 改為：WHERE bayymm IN ('11406', '11407', '11408')
-- =====================================================

-- =====================================================
-- 如果您要查詢多個經銷商，可以使用 IN 條件：
-- 
-- 將：AND a.abvenn = '32050'
-- 改為：AND a.abvenn IN ('32050', '32051', '32052')
-- =====================================================

-- =====================================================
-- 如果您要查詢所有經銷商的某個月份，只需要：
-- 1. 保留年月過濾：AND SUBSTR(a.abdate, 1, 5) = '11406'
-- 2. 移除經銷商過濾：-- AND a.abvenn = '32050'
-- 3. 保留 BTBA 的年月過濾：WHERE bayymm = '11406'
-- =====================================================
