-- =====================================================
-- 正確的條件優化查詢
-- 解決加上條件後反而變慢的問題
-- =====================================================

-- 問題分析：
-- 您在最外層加上 AND ABYYMM = '11406' AND ABVENN = '32050'
-- 但這時資料已經經過複雜的 UNION ALL 和連接處理
-- 正確的做法是在每個 UNION 分支的最內層就先過濾

-- 優化版本 1：在每個分支最內層加上過濾條件
SELECT ABYYMM, ABVENN, ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2),
               'yyyymmdd') ABDATE,
       ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
       NULL AASTOP, CUST_NO, NULL, NULL, NULL, AAKIND,
       AADISP,
       AAROUT,
       AMT,
       TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
       ABVENN || ABCSTN ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
FROM (
    SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01',
                   'yyyymmdd') ABYYMM, 
           C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
           C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO, NULL,
           NULL, NULL, NULL AAKIND,
           NULL AADISP,
           C.AAROUT,
           ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT, 
           B.BAPRIC,
           B.BARATE,
           B.BARAT1
    FROM (
        -- 第一個 UNION：ASAB + SBAI + ASAA (在這裡加上過濾條件)
        SELECT SUBSTR(A.ABDATE, 1, 5) ABYYMM, 
               A.ABVENN, 
               C.AAITEM TRADE_TYPE, 
               A.ABCSTN, 
               A.ABDATE, 
               C.AADATE,
               C.AASTOP, 
               C.AAROUT, 
               A.ABITNO, 
               DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2, 
               A.ABOQTY,
               A.ABTXAT, 
               A.ABAMTE, 
               A.ABRQTY, 
               A.ABRTXA, 
               A.ABRAMT, 
               A.ABPQTY, 
               A.ABORSD,
               DECODE(A.ABTYPE, 'V', 'VM', 'O') ABTYPE, 
               A.ABVENN || A.ABCSTN CUST_NO,
               (A.ABOQTY + A.ABPQTY - A.ABRQTY) QTY
        FROM ASAB A, SBAI B, ASAA C
        WHERE A.ABITNO = B.AIITNO (+) 
          AND A.ABVENN = C.AAVENN 
          AND A.ABCSTN = C.AACSTN 
          AND C.AAITEM IS NOT NULL
          -- 關鍵優化：在最內層就過濾
          AND SUBSTR(A.ABDATE, 1, 5) = '11406'  -- 直接用字串比較年月
          AND A.ABVENN = '32050'                -- 直接過濾經銷商
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
        
        UNION ALL
        
        -- 第二個 UNION：ASFA + ASHC (同樣在內層過濾)
        SELECT SUBSTR(A.FADATE, 1, 5) ABYYMM, 
               A.FAVENN, 
               C.HCITEM TRADE_TYPE, 
               A.FACSTN, 
               A.FADATE, 
               C.HCCRDT, 
               C.HCSTDT, 
               C.HCROUT,
               NULL FAITNO, 
               A.FAITNO FAITNO2, 
               A.FASQTY, 
               A.FAAMTE, 
               0, 
               0, 
               0, 
               0, 
               0, 
               NULL, 
               NULL, 
               A.FAVENN || A.FACSTN CUST_NO,
               A.FASQTY
        FROM ASFA A, ASHC C
        WHERE A.FAVENN = C.HCVENN 
          AND A.FACSTN = C.HCCSTN 
          AND A.FAROUT = C.HCROUT 
          -- 關鍵優化：在最內層就過濾
          AND SUBSTR(A.FADATE, 1, 5) = '11406'  -- 直接用字串比較年月
          AND A.FAVENN = '32050'                -- 直接過濾經銷商
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FADATE, 1, 3)) + 1911) || SUBSTR(A.FADATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
        
        UNION ALL
        
        -- 第三個 UNION：ASFF + ASHC (同樣在內層過濾)
        SELECT SUBSTR(A.FFDATE, 1, 5) ABYYMM, 
               A.FFVENN, 
               C.HCITEM TRADE_TYPE, 
               A.FFVENN FFCSTN, 
               A.FFDATE, 
               C.HCCRDT, 
               C.HCSTDT,
               C.HCROUT, 
               NULL FFITNO, 
               A.FFITNO FAITNO2, 
               A.FFOQTY * 12,
               0, 
               A.FFAMTE FFAMTE, 
               0, 
               0, 
               0, 
               0, 
               NULL, 
               'EX', 
               A.FFVENN CUST_NO, 
               A.FFOQTY * 12
        FROM ASFF A, ASHC C
        WHERE A.FFVENN = C.HCVENN 
          -- 關鍵優化：在最內層就過濾
          AND SUBSTR(A.FFDATE, 1, 5) = '11406'  -- 直接用字串比較年月
          AND A.FFVENN = '32050'                -- 直接過濾經銷商
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FFDATE, 1, 3)) + 1911) || SUBSTR(A.FFDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    ) C,
    (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1 
     FROM BTBA 
     WHERE BAYYMM = '11406') B  -- 也在 BTBA 表上加上過濾
    WHERE C.ABYYMM = B.BAYYMM 
      AND C.ABITNO2 = B.BAITNO 
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(C.ABDATE, 1, 3)) + 1911) || SUBSTR(C.ABDATE, 4, 4),
                  'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
);

-- =====================================================
-- 為什麼您的方式會變慢：
-- 
-- 1. 您在最外層加條件：
--    - 資料已經經過複雜的 UNION ALL 處理
--    - 所有三個分支的資料都已經被處理和合併
--    - 然後才在最後過濾，浪費了大量計算資源
-- 
-- 2. 正確的優化方式：
--    - 在每個 UNION 分支的最內層就加上過濾條件
--    - 使用 SUBSTR(A.ABDATE, 1, 5) = '11406' 直接字串比較
--    - 在 BTBA 表上也加上 WHERE BAYYMM = '11406'
--    - 這樣可以大幅減少需要處理的資料量
-- 
-- 3. 效能改善原理：
--    - 早期過濾：在資料來源就減少資料量
--    - 避免不必要的 UNION ALL 處理
--    - 減少連接操作的資料量
--    - 字串比較比日期函數比較更快
-- 
-- 預期效果：
-- - 查詢時間從分鐘級降到秒級
-- - 記憶體使用大幅減少
-- - CPU 使用率顯著降低
-- =====================================================
