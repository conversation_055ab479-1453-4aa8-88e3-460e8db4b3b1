-- =====================================================
-- Power BI VIEW 優化解決方案
-- 解決 VIEW 無法使用參數的問題
-- =====================================================

-- 方案 1：建立預先聚合的物化視圖 (推薦)
-- 優點：查詢速度最快，Power BI 可以直接使用
-- 缺點：需要定期重新整理

DROP MATERIALIZED VIEW ASUSER.MV_FACT_ASAB_MONTHLY;

CREATE MATERIALIZED VIEW ASUSER.MV_FACT_ASAB_MONTHLY
BUILD IMMEDIATE
REFRESH FAST ON DEMAND
AS
SELECT SUBSTR(ABDATE, 1, 5) as ABYYMM,
       ABVENN,
       ABCSTN,
       ABITNO2 as ABITNO,
       TRADE_TYPE,
       AAROUT,
       SUM(ABOQTY) as ABOQTY,
       SUM(ABTXAT) as ABTXAT,
       SUM(ABAMTE) as ABAMTE,
       SUM(ABRQTY) as ABRQTY,
       SUM(ABRTXA) as ABRTXA,
       SUM(ABRAMT) as ABRAMT,
       SUM(ABPQTY) as ABPQTY,
       SUM(ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0)) as AMT,
       COUNT(*) as RECORD_COUNT
FROM (
    -- 這裡放入您的完整優化查詢，但移除具體的過濾條件
    SELECT SUBSTR(A.ABDATE, 1, 5) ABDATE,
           A.ABVENN,
           A.ABCSTN,
           DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2,
           C.AAITEM TRADE_TYPE,
           C.AAROUT,
           A.ABOQTY,
           A.ABTXAT,
           A.ABAMTE,
           A.ABRQTY,
           A.ABRTXA,
           A.ABRAMT,
           A.ABPQTY,
           (A.ABOQTY + A.ABPQTY - A.ABRQTY) QTY,
           BT.BAPRIC,
           BT.BARATE,
           BT.BARAT1
    FROM ASAB A, SBAI B, ASAA C, BTBA BT
    WHERE A.ABITNO = B.AIITNO (+)
      AND A.ABVENN = C.AAVENN
      AND A.ABCSTN = C.AACSTN
      AND C.AAITEM IS NOT NULL
      AND SUBSTR(A.ABDATE, 1, 5) = BT.BAYYMM
      AND DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) = BT.BAITNO
      -- 只保留最近36個月的資料
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4), 'yyyymmdd') 
          BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    
    UNION ALL
    
    SELECT SUBSTR(A.FADATE, 1, 5) ABDATE,
           A.FAVENN,
           A.FACSTN,
           A.FAITNO ABITNO2,
           C.HCITEM TRADE_TYPE,
           C.HCROUT,
           A.FASQTY,
           A.FAAMTE,
           0,
           0,
           0,
           0,
           0,
           A.FASQTY QTY,
           BT.BAPRIC,
           BT.BARATE,
           BT.BARAT1
    FROM ASFA A, ASHC C, BTBA BT
    WHERE A.FAVENN = C.HCVENN
      AND A.FACSTN = C.HCCSTN
      AND A.FAROUT = C.HCROUT
      AND SUBSTR(A.FADATE, 1, 5) = BT.BAYYMM
      AND A.FAITNO = BT.BAITNO
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FADATE, 1, 3)) + 1911) || SUBSTR(A.FADATE, 4, 4), 'yyyymmdd') 
          BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    
    UNION ALL
    
    SELECT SUBSTR(A.FFDATE, 1, 5) ABDATE,
           A.FFVENN,
           A.FFVENN,
           A.FFITNO ABITNO2,
           C.HCITEM TRADE_TYPE,
           C.HCROUT,
           A.FFOQTY * 12,
           0,
           A.FFAMTE,
           0,
           0,
           0,
           0,
           A.FFOQTY * 12 QTY,
           BT.BAPRIC,
           BT.BARATE,
           BT.BARAT1
    FROM ASFF A, ASHC C, BTBA BT
    WHERE A.FFVENN = C.HCVENN
      AND SUBSTR(A.FFDATE, 1, 5) = BT.BAYYMM
      AND A.FFITNO = BT.BAITNO
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FFDATE, 1, 3)) + 1911) || SUBSTR(A.FFDATE, 4, 4), 'yyyymmdd') 
          BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
)
GROUP BY SUBSTR(ABDATE, 1, 5), ABVENN, ABCSTN, ABITNO2, TRADE_TYPE, AAROUT;

-- 為物化視圖建立索引
CREATE INDEX idx_mv_asab_abyymm ON ASUSER.MV_FACT_ASAB_MONTHLY (ABYYMM);
CREATE INDEX idx_mv_asab_venn ON ASUSER.MV_FACT_ASAB_MONTHLY (ABVENN);
CREATE INDEX idx_mv_asab_abitno ON ASUSER.MV_FACT_ASAB_MONTHLY (ABITNO);
CREATE INDEX idx_mv_asab_composite ON ASUSER.MV_FACT_ASAB_MONTHLY (ABYYMM, ABVENN, ABITNO);

-- Power BI 使用的 VIEW (指向物化視圖)
CREATE OR REPLACE VIEW ASUSER.FACT_ASAB_AD AS
SELECT ABYYMM,
       ABVENN,
       ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01', 'yyyymmdd') as ABDATE,
       ABITNO,
       ABOQTY,
       ABTXAT,
       ABAMTE,
       ABRQTY,
       ABRTXA,
       ABRAMT,
       ABPQTY,
       NULL as ABORSD,
       NULL as ABTYPE,
       NULL as AASTOP,
       ABVENN || ABCSTN as CUST_NO,
       NULL as PM,
       NULL as BRAND,
       NULL as KIND,
       NULL as AAKIND,
       NULL as AADISP,
       AAROUT,
       AMT,
       TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01', 'yyyymmdd'), 'YYYYMM') || ABITNO as YYMM_ABITNO,
       ABVENN || ABCSTN as ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE as ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) as ABVENN_ABCSTN_LARGE
FROM ASUSER.MV_FACT_ASAB_MONTHLY;

-- =====================================================
-- 方案 2：建立分割的 VIEW (按年月分割)
-- 適用於只查詢特定月份的情況
-- =====================================================

-- 當月 VIEW
CREATE OR REPLACE VIEW ASUSER.FACT_ASAB_CURRENT_MONTH AS
SELECT ABYYMM, ABVENN, ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2), 'yyyymmdd') ABDATE,
       ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
       NULL AASTOP, CUST_NO, NULL AAA1, NULL AAA2, NULL AAA3, AAKIND, AADISP, AAROUT, AMT,
       TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
       ABVENN || ABCSTN ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
FROM (
    -- 這裡放入優化查詢，但只查詢當月
    -- 使用 TO_CHAR(SYSDATE, 'YYYYMM') 動態獲取當月
    SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01', 'yyyymmdd') ABYYMM,
           C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
           C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO,
           NULL, NULL, NULL, NULL AAKIND, NULL AADISP, C.AAROUT,
           ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT,
           B.BAPRIC, B.BARATE, B.BARAT1
    FROM (
        SELECT SUBSTR(A.ABDATE, 1, 5) ABYYMM, A.ABVENN, C.AAITEM TRADE_TYPE, A.ABCSTN, A.ABDATE,
               C.AADATE, C.AASTOP, C.AAROUT, A.ABITNO,
               DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2,
               A.ABOQTY, A.ABTXAT, A.ABAMTE, A.ABRQTY, A.ABRTXA, A.ABRAMT, A.ABPQTY, A.ABORSD,
               DECODE(A.ABTYPE, 'V', 'VM', 'O') ABTYPE, A.ABVENN || A.ABCSTN CUST_NO,
               (A.ABOQTY + A.ABPQTY - A.ABRQTY) QTY
        FROM ASAB A, SBAI B, ASAA C
        WHERE A.ABITNO = B.AIITNO (+)
          AND A.ABVENN = C.AAVENN
          AND A.ABCSTN = C.AACSTN
          AND C.AAITEM IS NOT NULL
          -- 動態查詢當月
          AND SUBSTR(A.ABDATE, 1, 5) = LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || TO_CHAR(SYSDATE, 'MM')
    ) C,
    (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1 FROM BTBA 
     WHERE BAYYMM = LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || TO_CHAR(SYSDATE, 'MM')) B
    WHERE C.ABYYMM = B.BAYYMM AND C.ABITNO2 = B.BAITNO
);

-- =====================================================
-- 方案 3：使用函數式 VIEW (推薦給 Power BI)
-- =====================================================

-- 建立函數來動態產生查詢結果
CREATE OR REPLACE FUNCTION GET_ASAB_DATA(
    p_start_month VARCHAR2 DEFAULT NULL,
    p_end_month VARCHAR2 DEFAULT NULL,
    p_vendor VARCHAR2 DEFAULT NULL
) RETURN SYS_REFCURSOR
IS
    v_cursor SYS_REFCURSOR;
    v_sql VARCHAR2(32000);
BEGIN
    v_sql := 'SELECT ABYYMM, ABVENN, ABCSTN, ABDATE, ABITNO, ABOQTY, ABTXAT, ABAMTE, 
                     ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE, AASTOP, CUST_NO,
                     PM, BRAND, KIND, AAKIND, AADISP, AAROUT, AMT, YYMM_ABITNO,
                     ABVENN_ABCSTN, ABVENN_ABCSTN_TYPE, ABVENN_ABCSTN_LARGE
              FROM (您的優化查詢) 
              WHERE 1=1';
    
    IF p_start_month IS NOT NULL THEN
        v_sql := v_sql || ' AND SUBSTR(ABDATE, 1, 5) >= ''' || p_start_month || '''';
    END IF;
    
    IF p_end_month IS NOT NULL THEN
        v_sql := v_sql || ' AND SUBSTR(ABDATE, 1, 5) <= ''' || p_end_month || '''';
    END IF;
    
    IF p_vendor IS NOT NULL THEN
        v_sql := v_sql || ' AND ABVENN = ''' || p_vendor || '''';
    END IF;
    
    OPEN v_cursor FOR v_sql;
    RETURN v_cursor;
END;
/

-- =====================================================
-- 建議的實施策略：
-- 
-- 1. 短期解決方案：使用方案1的物化視圖
--    - 每天晚上重新整理物化視圖
--    - Power BI 查詢速度會非常快
-- 
-- 2. 長期解決方案：
--    - 在 Power BI 中使用參數和動態查詢
--    - 或者建立多個特定用途的 VIEW
-- 
-- 3. 維護策略：
--    - 設定自動重新整理物化視圖的排程
--    - 監控物化視圖的大小和效能
-- =====================================================
