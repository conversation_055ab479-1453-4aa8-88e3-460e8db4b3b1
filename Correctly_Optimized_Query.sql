-- =====================================================
-- 正確的優化查詢 - 在最內層就加上所有過濾條件
-- =====================================================

SELECT ABYYMM, ABVENN, ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2),
               'yyyymmdd') ABDATE,
       ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
       NULL AASTOP, CUST_NO, NULL AAA1, NULL AAA2, NULL AAA3, AAKIND,
       AADISP,
       AAROUT,
       AMT,
       TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
       ABVENN || ABCSTN ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
FROM (
    SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01',
                   'yyyymmdd') ABYYMM,
           C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
           C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO, NULL,
           NULL, NULL, NULL AAKIND,
           NULL AADISP,
           C.AAROUT,
           ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT,
           B.BAPRIC,
           B.BARATE,
           B.BARAT1
    FROM (
        -- 第一個 UNION：ASAB + SBAI + ASAA (在最內層加上所有過濾條件)
        SELECT SUBSTR(A.ABDATE, 1, 5) ABYYMM,
               A.ABVENN,
               C.AAITEM TRADE_TYPE,
               A.ABCSTN,
               A.ABDATE,
               C.AADATE,
               C.AASTOP,
               C.AAROUT,
               A.ABITNO,
               DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2,
               A.ABOQTY,
               A.ABTXAT,
               A.ABAMTE,
               A.ABRQTY,
               A.ABRTXA,
               A.ABRAMT,
               A.ABPQTY,
               A.ABORSD,
               DECODE(A.ABTYPE, 'V', 'VM', 'O') ABTYPE,
               A.ABVENN || A.ABCSTN CUST_NO,
               (A.ABOQTY + A.ABPQTY - A.ABRQTY) QTY
        FROM ASAB A, SBAI B, ASAA C
        WHERE A.ABITNO = B.AIITNO (+)
          AND A.ABVENN = C.AAVENN
          AND A.ABCSTN = C.AACSTN
          AND C.AAITEM IS NOT NULL
          -- 關鍵優化：在最內層就加上所有過濾條件
          AND SUBSTR(A.ABDATE, 1, 5) = '11406'  -- 年月過濾
          AND A.ABVENN = '32050'                -- 經銷商過濾
          AND DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) = '1824'  -- 產品過濾
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE

        UNION ALL

        -- 第二個 UNION：ASFA + ASHC (在最內層加上所有過濾條件)
        SELECT SUBSTR(A.FADATE, 1, 5) ABYYMM,
               A.FAVENN,
               C.HCITEM TRADE_TYPE,
               A.FACSTN,
               A.FADATE,
               C.HCCRDT,
               C.HCSTDT,
               C.HCROUT,
               NULL FAITNO,
               A.FAITNO FAITNO2,
               A.FASQTY,
               A.FAAMTE,
               0,
               0,
               0,
               0,
               0,
               NULL,
               NULL,
               A.FAVENN || A.FACSTN CUST_NO,
               A.FASQTY
        FROM ASFA A, ASHC C
        WHERE A.FAVENN = C.HCVENN
          AND A.FACSTN = C.HCCSTN
          AND A.FAROUT = C.HCROUT
          -- 關鍵優化：在最內層就加上所有過濾條件
          AND SUBSTR(A.FADATE, 1, 5) = '11406'  -- 年月過濾
          AND A.FAVENN = '32050'                -- 經銷商過濾
          AND A.FAITNO = '1824'                 -- 產品過濾
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FADATE, 1, 3)) + 1911) || SUBSTR(A.FADATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE

        UNION ALL

        -- 第三個 UNION：ASFF + ASHC (在最內層加上所有過濾條件)
        SELECT SUBSTR(A.FFDATE, 1, 5) ABYYMM,
               A.FFVENN,
               C.HCITEM TRADE_TYPE,
               A.FFVENN FFCSTN,
               A.FFDATE,
               C.HCCRDT,
               C.HCSTDT,
               C.HCROUT,
               NULL FFITNO,
               A.FFITNO FAITNO2,
               A.FFOQTY * 12,
               0,
               A.FFAMTE FFAMTE,
               0,
               0,
               0,
               0,
               NULL,
               'EX',
               A.FFVENN CUST_NO,
               A.FFOQTY * 12
        FROM ASFF A, ASHC C
        WHERE A.FFVENN = C.HCVENN
          -- 關鍵優化：在最內層就加上所有過濾條件
          AND SUBSTR(A.FFDATE, 1, 5) = '11406'  -- 年月過濾
          AND A.FFVENN = '32050'                -- 經銷商過濾
          AND A.FFITNO = '1824'                 -- 產品過濾
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.FFDATE, 1, 3)) + 1911) || SUBSTR(A.FFDATE, 4, 4),
                      'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
    ) C,
    (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1
     FROM BTBA
     WHERE BAYYMM = '11406'
       AND BAITNO = '1824') B  -- 在 BTBA 表上也加上所有過濾條件
    WHERE C.ABYYMM = B.BAYYMM
      AND C.ABITNO2 = B.BAITNO
      AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(C.ABDATE, 1, 3)) + 1911) || SUBSTR(C.ABDATE, 4, 4),
                  'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
);

-- =====================================================
-- 關鍵修正：
-- 
-- 1. 移除最外層的 WHERE 條件
-- 2. 在每個 UNION 分支的最內層加上：
--    - AND SUBSTR(A.ABDATE, 1, 5) = '11406'  (年月)
--    - AND A.ABVENN = '32050'                (經銷商)
--    - AND A.ABITNO = '1824'                 (產品)
-- 
-- 3. 在 BTBA 表上也加上：
--    - WHERE BAYYMM = '11406' AND BAITNO = '1824'
-- 
-- 這樣可以在資料來源就大幅減少資料量，
-- 避免處理不需要的資料後再過濾。
-- =====================================================
