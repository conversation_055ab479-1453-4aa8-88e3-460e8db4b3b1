-- =====================================================
-- 修正版本的完整優化查詢
-- 確保所有語法正確，可直接執行
-- =====================================================

WITH 
-- 步驟1：預先計算日期邊界
date_boundaries AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36) as start_date,
        SYSDATE as end_date,
        LPAD(TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36), 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36), 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
),

-- 步驟2：預先過濾 BTBA 價格表
filtered_btba AS (
    SELECT 
        BAYYMM,
        BAITNO,
        BAPRIC,
        BARATE,
        BARAT1
    FROM BTBA
    WHERE BAPRIC > 0 
      AND BARATE > 0
),

-- 步驟3：第一個 UNION 分支 (ASAB + SBAI + ASAA)
union_branch_1 AS (
    SELECT 
        SUBSTR(a.ABDATE, 1, 5) as ABYYMM,
        a.ABVENN,
        c.AAITEM as TRADE_TYPE,
        a.ABCSTN,
        a.ABDATE,
        c.AADATE,
        c.AASTOP,
        c.AAROUT,
        a.ABITNO,
        CASE WHEN b.AIITNO IS NOT NULL THEN b.AIITNO2 ELSE a.ABITNO END as ABITNO2,
        a.ABOQTY,
        a.ABTXAT,
        a.ABAMTE,
        a.ABRQTY,
        a.ABRTXA,
        a.ABRAMT,
        a.ABPQTY,
        a.ABORSD,
        CASE a.ABTYPE WHEN 'V' THEN 'VM' ELSE 'O' END as ABTYPE,
        a.ABVENN || a.ABCSTN as CUST_NO,
        (a.ABOQTY + a.ABPQTY - a.ABRQTY) as QTY
    FROM ASAB a
    LEFT JOIN SBAI b ON a.ABITNO = b.AIITNO
    INNER JOIN ASAA c ON a.ABVENN = c.AAVENN AND a.ABCSTN = c.AACSTN
    CROSS JOIN date_boundaries db
    WHERE c.AAITEM IS NOT NULL
      AND a.ABDATE BETWEEN db.start_roc_date AND db.end_roc_date
),

-- 步驟4：第二個 UNION 分支 (ASFA + ASHC)
union_branch_2 AS (
    SELECT
        SUBSTR(a.FADATE, 1, 5) as ABYYMM,
        a.FAVENN as ABVENN,
        c.HCITEM as TRADE_TYPE,
        a.FACSTN as ABCSTN,
        a.FADATE as ABDATE,
        c.HCCRDT as AADATE,
        c.HCSTDT as AASTOP,
        c.HCROUT as AAROUT,
        CAST(NULL AS VARCHAR2(20)) as ABITNO,
        a.FAITNO as ABITNO2,
        a.FASQTY as ABOQTY,
        a.FAAMTE as ABTXAT,
        0 as ABAMTE,
        0 as ABRQTY,
        0 as ABRTXA,
        0 as ABRAMT,
        0 as ABPQTY,
        0 as ABORSD,
        CAST(NULL AS VARCHAR2(10)) as ABTYPE,
        a.FAVENN || a.FACSTN as CUST_NO,
        a.FASQTY as QTY
    FROM ASFA a
    INNER JOIN ASHC c ON a.FAVENN = c.HCVENN
                     AND a.FACSTN = c.HCCSTN
                     AND a.FAROUT = c.HCROUT
    CROSS JOIN date_boundaries db
    WHERE a.FADATE BETWEEN db.start_roc_date AND db.end_roc_date
),

-- 步驟5：第三個 UNION 分支 (ASFF + ASHC)
union_branch_3 AS (
    SELECT
        SUBSTR(a.FFDATE, 1, 5) as ABYYMM,
        a.FFVENN as ABVENN,
        c.HCITEM as TRADE_TYPE,
        a.FFVENN as ABCSTN,
        a.FFDATE as ABDATE,
        c.HCCRDT as AADATE,
        c.HCSTDT as AASTOP,
        c.HCROUT as AAROUT,
        CAST(NULL AS VARCHAR2(20)) as ABITNO,
        a.FFITNO as ABITNO2,
        a.FFOQTY * 12 as ABOQTY,  -- 打數轉換成瓶罐
        0 as ABTXAT,
        a.FFAMTE as ABAMTE,
        0 as ABRQTY,
        0 as ABRTXA,
        0 as ABRAMT,
        0 as ABPQTY,
        0 as ABORSD,
        'EX' as ABTYPE,
        a.FFVENN as CUST_NO,
        a.FFOQTY * 12 as QTY  -- 打數轉換成瓶罐
    FROM ASFF a
    INNER JOIN ASHC c ON a.FFVENN = c.HCVENN
    CROSS JOIN date_boundaries db
    WHERE a.FFDATE BETWEEN db.start_roc_date AND db.end_roc_date
),

-- 步驟6：合併所有 UNION 分支
combined_data AS (
    SELECT * FROM union_branch_1
    UNION ALL
    SELECT * FROM union_branch_2  
    UNION ALL
    SELECT * FROM union_branch_3
),

-- 步驟7：與價格表連接並計算最終結果
final_calculation AS (
    SELECT 
        -- 轉換 ABYYMM 為標準日期格式
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd') as ABYYMM,
        c.ABVENN,
        c.ABCSTN,
        -- 轉換 ABDATE 為標準日期格式
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABDATE, 1, 3)) + 1911) || SUBSTR(c.ABDATE, 4, 2) || SUBSTR(c.ABDATE, 6, 2), 'yyyymmdd') as ABDATE,
        c.ABITNO,
        c.ABOQTY,
        c.ABTXAT,
        c.ABAMTE,
        c.ABRQTY,
        c.ABRTXA,
        c.ABRAMT,
        c.ABPQTY,
        c.ABORSD,
        c.TRADE_TYPE,
        NULL as AASTOP,
        c.CUST_NO,
        NULL as col1,
        NULL as col2,
        NULL as col3,
        NULL as AAKIND,
        NULL as AADISP,
        c.AAROUT,
        -- 計算金額
        ROUND(ROUND(c.QTY * b.BAPRIC / b.BARATE, 0) * (1 - b.BARAT1), 0) as AMT,
        -- 計算組合欄位
        TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd'), 'YYYYMM') || c.ABITNO as YYMM_ABITNO,
        c.ABVENN || c.ABCSTN as ABVENN_ABCSTN,
        c.ABVENN || c.ABCSTN || c.TRADE_TYPE as ABVENN_ABCSTN_TYPE,
        c.ABVENN || c.ABCSTN || SUBSTR(c.AAROUT, 1, 1) as ABVENN_ABCSTN_LARGE
    FROM combined_data c
    INNER JOIN filtered_btba b ON c.ABYYMM = b.BAYYMM AND c.ABITNO2 = b.BAITNO
    WHERE c.QTY > 0
      AND b.BAPRIC > 0
)

-- 最終查詢結果
SELECT 
    ABYYMM,
    ABVENN,
    ABCSTN,
    ABDATE,
    ABITNO,
    ABOQTY,
    ABTXAT,
    ABAMTE,
    ABRQTY,
    ABRTXA,
    ABRAMT,
    ABPQTY,
    ABORSD,
    TRADE_TYPE,
    AASTOP,
    CUST_NO,
    col1,
    col2,
    col3,
    AAKIND,
    AADISP,
    AAROUT,
    AMT,
    YYMM_ABITNO,
    ABVENN_ABCSTN,
    ABVENN_ABCSTN_TYPE,
    ABVENN_ABCSTN_LARGE
FROM final_calculation
ORDER BY ABYYMM DESC, ABVENN, ABCSTN, ABITNO;

-- =====================================================
-- 主要優化點：
-- 1. 預先計算日期邊界，避免重複的 ROC 日期轉換
-- 2. 使用字串比較替代日期函數比較 (WHERE 條件)
-- 3. 結構化 CTE 提高查詢可讀性
-- 4. 預先過濾價格表，減少連接資料量
-- 5. 正確使用 CROSS JOIN 處理日期邊界
-- =====================================================
