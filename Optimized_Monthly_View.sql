-- =====================================================
-- 優化的月彙總 VIEW - FACT_ASAB_AD_MONTH
-- 只查詢最近12個月，大幅提升 Power BI 效能
-- =====================================================

DROP VIEW ASUSER.FACT_ASAB_AD_MONTH;

CREATE OR REPLACE FORCE VIEW ASUSER.FACT_ASAB_AD_MONTH
(ABYYMM, ABVENN, ABCSTN, ABDATE, ABITNO, 
 ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, 
 ABRAMT, ABPQTY, ABORSD, ABTYPE, AASTOP, 
 CUST_NO, PM, BRAND, KIND, AAKIND, 
 AADISP, AAROUT, AMT, YYMM_ABITNO, ABVENN_ABCSTN, 
 ABVENN_ABCSTN_TYPE, ABVENN_ABCSTN_LARGE)
AS 
SELECT  abyymm,
        abvenn,
        abcstn,
        abdate,
        abitno,
        sum(aboqty) aboqty,
        sum(abtxat) abtxat,
        sum(abamte) abamte,
        sum(abrqty) abrqty,
        sum(abrtxa) abrtxa,
        sum(abramt) abramt,
        sum(abpqty) abpqty,
        aborsd,
        trade_type,
        aastop,    
        CUST_NO, 
        PM, 
        BRAND, 
        KIND, 
        aakind,
        aadisp,
        aarout,
        sum(amt) amt,
        yymm_abitno,
        abvenn_abcstn,
        abvenn_abcstn_type,
        ABVENN_ABCSTN_LARGE
FROM (
    SELECT abyymm,
           abvenn,
           abcstn,
           TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 2) || '01', 'yyyymmdd') abdate,
           abitno,
           aboqty,
           abtxat,
           abamte,
           abrqty,
           abrtxa,
           abramt,
           abpqty,
           aborsd,
           trade_type,
           NULL aastop,    
           cust_no,
           NULL PM,
           NULL BRAND,
           NULL KIND,
           aakind,
           aadisp,
           aarout,
           amt,
           TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno,
           abvenn||abcstn abvenn_abcstn,
           abvenn||abcstn||trade_type abvenn_abcstn_type,
           abvenn||abcstn||SUBSTR(aarout, 1, 1) ABVENN_ABCSTN_LARGE
    FROM (
        SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd') abyymm,
               c.abvenn,
               c.abcstn,
               c.abdate,
               c.abitno2 abitno,
               c.aboqty,
               c.abtxat,
               c.abamte,
               c.abrqty,
               c.abrtxa,
               c.abramt,
               c.abpqty,
               c.aborsd,
               c.trade_type,
               c.aadate,
               c.aastop,
               c.cust_no,
               NULL,
               NULL,
               NULL,
               NULL aakind,
               NULL aadisp,
               c.aarout,
               ROUND((ROUND((qty * bapric) / barate, 0) * (1 - barat1)), 0) amt,
               b.bapric,
               b.barate,
               b.barat1
        FROM (
            -- 第一個 UNION：ASAB + SBAI + ASAA (優化：只查詢最近12個月)
            SELECT SUBSTR(a.abdate, 1, 5) abyymm,
                   a.abvenn,
                   c.aaitem trade_type,
                   a.abcstn,
                   a.abdate,
                   c.aadate,
                   c.aastop,
                   c.aarout,
                   a.abitno,
                   DECODE(a.abitno, b.aiitno, b.aiitno2, a.abitno) abitno2,
                   a.aboqty,
                   a.abtxat,
                   a.abamte,
                   a.abrqty,
                   a.abrtxa,
                   a.abramt,
                   a.abpqty,
                   a.aborsd,
                   DECODE(a.abtype, 'V', 'VM', 'O') abtype,
                   a.abvenn || a.abcstn cust_no,
                   (a.aboqty + a.abpqty - a.abrqty) qty
            FROM asab a, sbai b, asaa c
            WHERE a.abitno = b.aiitno(+)
              AND a.abvenn = c.aavenn
              AND a.abcstn = c.aacstn
              AND c.aaitem IS NOT NULL
              -- 關鍵優化：只查詢最近12個月，大幅減少資料量
              AND SUBSTR(a.abdate, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                            TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
            
            UNION ALL
            
            -- 第二個 UNION：ASFA + ASHC (同樣只查詢最近12個月)
            SELECT SUBSTR(a.fadate, 1, 5) abyymm,
                   a.favenn,
                   c.hcitem trade_type,
                   a.facstn,
                   a.fadate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL faitno,
                   a.faitno faitno2,
                   a.fasqty,
                   a.faamte,
                   0,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   NULL,
                   a.favenn || a.facstn cust_no,
                   a.fasqty
            FROM asfa a, ashc c
            WHERE a.favenn = c.hcvenn
              AND a.facstn = c.hccstn
              AND a.farout = c.hcrout
              -- 關鍵優化：只查詢最近12個月
              AND SUBSTR(a.fadate, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                            TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.fadate, 1, 3)) + 1911) || SUBSTR(a.fadate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
            
            UNION ALL
            
            -- 第三個 UNION：ASFF + ASHC (同樣只查詢最近12個月)
            SELECT SUBSTR(a.ffdate, 1, 5) abyymm,
                   a.ffvenn,
                   c.hcitem trade_type,
                   a.ffvenn ffcstn,
                   a.ffdate,
                   c.HCCRDT,
                   c.HCSTDT,
                   c.HCROUT,
                   NULL ffitno,
                   a.ffitno faitno2,
                   a.ffoqty * 12, -- 將ASFF的單位-打數，*12換算成瓶罐
                   0,
                   a.ffamte ffamte,
                   0,
                   0,
                   0,
                   0,
                   NULL,
                   'EX',
                   a.ffvenn cust_no,
                   a.ffoqty * 12 -- 將ASFF的單位-打數，*12換算成瓶罐
            FROM ASFF a, ashc c
            WHERE a.ffvenn = c.hcvenn
              -- 關鍵優化：只查詢最近12個月
              AND SUBSTR(a.ffdate, 1, 5) >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                                            TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')
              AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.ffdate, 1, 3)) + 1911) || SUBSTR(a.ffdate, 4, 4), 'yyyymmdd') 
                  BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
        ) c,
        (SELECT bayymm, baitno, bapric, barate, barat1 
         FROM btba 
         WHERE bayymm >= LPAD(TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYY') - 1911, 3, '0') || 
                         TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'MM')) b
        WHERE c.abyymm = b.bayymm
          AND c.abitno2 = b.baitno
          AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.abdate, 1, 3)) + 1911) || SUBSTR(c.abdate, 4, 4), 'yyyymmdd') 
              BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -12) AND SYSDATE
    )
)
GROUP BY abyymm,
         abvenn,
         abcstn,
         abdate,
         abitno,
         aborsd,
         trade_type,
         aastop,    
         CUST_NO, 
         PM, 
         BRAND, 
         KIND, 
         aakind,
         aadisp,
         aarout,
         yymm_abitno,
         abvenn_abcstn,
         abvenn_abcstn_type,
         ABVENN_ABCSTN_LARGE;

-- =====================================================
-- 主要優化重點：
-- 
-- 1. 資料範圍限制：從36個月縮減到12個月
--    - 原始：ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36)
--    - 優化：ADD_MONTHS(SYSDATE, -12)
-- 
-- 2. 在每個 UNION 分支都加上月份過濾：
--    - SUBSTR(a.abdate, 1, 5) >= 最近12個月的起始月份
-- 
-- 3. 在 BTBA 表上也加上月份過濾：
--    - WHERE bayymm >= 最近12個月的起始月份
-- 
-- 4. 效能改善預期：
--    - 資料量減少約 66% (從36個月到12個月)
--    - 查詢速度提升 3-5 倍
--    - Power BI 載入速度大幅改善
-- 
-- 5. Power BI 使用範例：
--    SELECT * FROM ASUSER.FACT_ASAB_AD_MONTH 
--    WHERE ABVENN = '32050' 
--      AND TO_CHAR(ABYYMM, 'YYYYMM') = '202406'
-- 
-- 6. 如果需要更長的歷史資料：
--    - 可以調整 -12 為 -24 (2年) 或 -18 (1.5年)
--    - 平衡效能和資料需求
-- =====================================================
