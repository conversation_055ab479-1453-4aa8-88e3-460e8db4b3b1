-- =====================================================
-- 逐步測試腳本 - SQL 查詢優化驗證
-- 不建立新索引，純粹測試查詢重構效果
-- =====================================================

-- 使用說明：
-- 1. 每次只執行一個測試區塊
-- 2. 記錄每個測試的執行時間和成本
-- 3. 比較優化前後的差異
-- 4. 確認資料正確性

-- =====================================================
-- 測試準備：建立測試環境
-- =====================================================

-- 清除之前的測試結果
DELETE FROM plan_table WHERE statement_id LIKE 'TEST_%';

-- 建立測試結果記錄表
CREATE TABLE test_results (
    test_id VARCHAR2(50),
    test_description VARCHAR2(200),
    execution_time NUMBER,
    cost NUMBER,
    cardinality NUMBER,
    bytes_processed NUMBER,
    test_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 測試 1：ROC 日期轉換優化
-- =====================================================

PROMPT ========================================
PROMPT 測試 1：ROC 日期轉換優化
PROMPT ========================================

-- 測試 1A：原始日期轉換方法 (基準測試)
PROMPT 測試 1A：原始日期轉換方法

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_1A_ORIGINAL' FOR
SELECT COUNT(*), MIN(date_column), MAX(date_column)
FROM BTBA
WHERE TO_DATE((SUBSTR(date_column, 1, 3) + 1911) || SUBSTR(date_column, 4, 4), 'YYYYMMDD') 
      BETWEEN SYSDATE - 1095 AND SYSDATE;

-- 執行查詢並記錄時間
SELECT COUNT(*), MIN(date_column), MAX(date_column)
FROM BTBA
WHERE TO_DATE((SUBSTR(date_column, 1, 3) + 1911) || SUBSTR(date_column, 4, 4), 'YYYYMMDD') 
      BETWEEN SYSDATE - 1095 AND SYSDATE;

SET TIMING OFF

-- 記錄執行計畫
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_1A', '原始日期轉換', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_1A_ORIGINAL' AND id = 0;

-- 測試 1B：優化的字串比較方法
PROMPT 測試 1B：優化的字串比較方法

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_1B_OPTIMIZED' FOR
WITH date_boundaries AS (
    SELECT 
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE - 1095, 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
)
SELECT COUNT(*), MIN(b.date_column), MAX(b.date_column)
FROM BTBA b, date_boundaries db
WHERE b.date_column BETWEEN db.start_roc_date AND db.end_roc_date;

-- 執行優化查詢
WITH date_boundaries AS (
    SELECT 
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE - 1095, 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
)
SELECT COUNT(*), MIN(b.date_column), MAX(b.date_column)
FROM BTBA b, date_boundaries db
WHERE b.date_column BETWEEN db.start_roc_date AND db.end_roc_date;

SET TIMING OFF

-- 記錄執行計畫
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_1B', '優化日期轉換', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_1B_OPTIMIZED' AND id = 0;

-- 比較測試 1 的結果
PROMPT 測試 1 結果比較：
SELECT 
    test_id,
    test_description,
    cost,
    cardinality,
    ROUND((cost - LAG(cost) OVER (ORDER BY test_id)) / LAG(cost) OVER (ORDER BY test_id) * 100, 2) as cost_change_pct
FROM test_results 
WHERE test_id LIKE 'TEST_1%'
ORDER BY test_id;

-- =====================================================
-- 測試 2：連接順序優化
-- =====================================================

PROMPT ========================================
PROMPT 測試 2：連接順序優化
PROMPT ========================================

-- 測試 2A：原始連接順序
PROMPT 測試 2A：原始連接順序

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_2A_ORIGINAL' FOR
SELECT COUNT(*), SUM(b.amount_column)
FROM BTBA b
JOIN ASAB ab ON b.key_column = ab.key_column
JOIN ASAA aa ON ab.reference_column = aa.reference_column
WHERE b.date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101';

-- 執行原始查詢
SELECT COUNT(*), SUM(b.amount_column)
FROM BTBA b
JOIN ASAB ab ON b.key_column = ab.key_column
JOIN ASAA aa ON ab.reference_column = aa.reference_column
WHERE b.date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101';

SET TIMING OFF

-- 記錄結果
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_2A', '原始連接順序', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_2A_ORIGINAL' AND id = 0;

-- 測試 2B：優化的連接順序 (先過濾再連接)
PROMPT 測試 2B：優化的連接順序

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_2B_OPTIMIZED' FOR
WITH filtered_btba AS (
    SELECT key_column, amount_column, date_column
    FROM BTBA
    WHERE date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101'
      AND amount_column IS NOT NULL
),
filtered_asab AS (
    SELECT key_column, reference_column
    FROM ASAB 
    WHERE active_flag = 'Y'
)
SELECT COUNT(*), SUM(fb.amount_column)
FROM filtered_btba fb
JOIN filtered_asab fab ON fb.key_column = fab.key_column
JOIN ASAA aa ON fab.reference_column = aa.reference_column;

-- 執行優化查詢
WITH filtered_btba AS (
    SELECT key_column, amount_column, date_column
    FROM BTBA
    WHERE date_column >= LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101'
      AND amount_column IS NOT NULL
),
filtered_asab AS (
    SELECT key_column, reference_column
    FROM ASAB 
    WHERE active_flag = 'Y'
)
SELECT COUNT(*), SUM(fb.amount_column)
FROM filtered_btba fb
JOIN filtered_asab fab ON fb.key_column = fab.key_column
JOIN ASAA aa ON fab.reference_column = aa.reference_column;

SET TIMING OFF

-- 記錄結果
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_2B', '優化連接順序', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_2B_OPTIMIZED' AND id = 0;

-- =====================================================
-- 測試 3：UNION-ALL 優化
-- =====================================================

PROMPT ========================================
PROMPT 測試 3：UNION-ALL 優化
PROMPT ========================================

-- 測試 3A：原始 UNION-ALL 結構
PROMPT 測試 3A：原始 UNION-ALL 結構

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_3A_ORIGINAL' FOR
SELECT 'TYPE_A' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'ACTIVE'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
UNION ALL
SELECT 'TYPE_B' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'PENDING'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
UNION ALL
SELECT 'TYPE_C' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'COMPLETED'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101';

-- 執行原始查詢
SELECT 'TYPE_A' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'ACTIVE'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
UNION ALL
SELECT 'TYPE_B' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'PENDING'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
UNION ALL
SELECT 'TYPE_C' as record_type, COUNT(*), SUM(amount_column)
FROM BTBA 
WHERE status_column = 'COMPLETED'
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101';

SET TIMING OFF

-- 記錄結果
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_3A', '原始UNION-ALL', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_3A_ORIGINAL' AND id = 0;

-- 測試 3B：優化的單次掃描
PROMPT 測試 3B：優化的單次掃描

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_3B_OPTIMIZED' FOR
SELECT 
    status_column as record_type,
    COUNT(*),
    SUM(amount_column)
FROM BTBA
WHERE status_column IN ('ACTIVE', 'PENDING', 'COMPLETED')
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
GROUP BY status_column;

-- 執行優化查詢
SELECT 
    status_column as record_type,
    COUNT(*),
    SUM(amount_column)
FROM BTBA
WHERE status_column IN ('ACTIVE', 'PENDING', 'COMPLETED')
  AND date_column >= LPAD(TO_CHAR(SYSDATE - 365, 'YYYY') - 1911, 3, '0') || '0101'
GROUP BY status_column;

SET TIMING OFF

-- 記錄結果
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_3B', '優化UNION-ALL', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_3B_OPTIMIZED' AND id = 0;

-- =====================================================
-- 測試 4：完整的優化查詢
-- =====================================================

PROMPT ========================================
PROMPT 測試 4：完整的優化查詢
PROMPT ========================================

-- 測試 4：結合所有優化技術的完整查詢
PROMPT 測試 4：完整優化查詢

SET TIMING ON
EXPLAIN PLAN SET STATEMENT_ID = 'TEST_4_COMPLETE' FOR
WITH 
-- 預先計算日期邊界
date_boundaries AS (
    SELECT 
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101' as start_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || '1231' as end_date
    FROM dual
),
-- 先過濾主表
filtered_main AS (
    SELECT /*+ FIRST_ROWS(10000) */
        key_column,
        date_column,
        amount_column,
        status_column
    FROM BTBA, date_boundaries db
    WHERE date_column BETWEEN db.start_date AND db.end_date
      AND amount_column > 0
      AND status_column IN ('ACTIVE', 'PENDING', 'COMPLETED')
),
-- 過濾關聯表
filtered_asab AS (
    SELECT key_column, reference_column
    FROM ASAB 
    WHERE active_flag = 'Y'
),
-- 執行連接
joined_data AS (
    SELECT 
        fm.date_column,
        fm.amount_column,
        fm.status_column,
        aa.account_type
    FROM filtered_main fm
    JOIN filtered_asab fab ON fm.key_column = fab.key_column
    JOIN ASAA aa ON fab.reference_column = aa.reference_column
    WHERE aa.status_code IN ('APPROVED', 'PROCESSED')
)
-- 最終聚合
SELECT 
    SUBSTR(date_column, 1, 5) as year_month,
    status_column,
    account_type,
    COUNT(*) as transaction_count,
    SUM(amount_column) as total_amount,
    AVG(amount_column) as avg_amount
FROM joined_data
GROUP BY 
    SUBSTR(date_column, 1, 5),
    status_column,
    account_type
ORDER BY year_month DESC, status_column, account_type;

-- 執行完整優化查詢
WITH 
date_boundaries AS (
    SELECT 
        LPAD(TO_CHAR(SYSDATE - 1095, 'YYYY') - 1911, 3, '0') || '0101' as start_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || '1231' as end_date
    FROM dual
),
filtered_main AS (
    SELECT /*+ FIRST_ROWS(10000) */
        key_column,
        date_column,
        amount_column,
        status_column
    FROM BTBA, date_boundaries db
    WHERE date_column BETWEEN db.start_date AND db.end_date
      AND amount_column > 0
      AND status_column IN ('ACTIVE', 'PENDING', 'COMPLETED')
),
filtered_asab AS (
    SELECT key_column, reference_column
    FROM ASAB 
    WHERE active_flag = 'Y'
),
joined_data AS (
    SELECT 
        fm.date_column,
        fm.amount_column,
        fm.status_column,
        aa.account_type
    FROM filtered_main fm
    JOIN filtered_asab fab ON fm.key_column = fab.key_column
    JOIN ASAA aa ON fab.reference_column = aa.reference_column
    WHERE aa.status_code IN ('APPROVED', 'PROCESSED')
)
SELECT 
    SUBSTR(date_column, 1, 5) as year_month,
    status_column,
    account_type,
    COUNT(*) as transaction_count,
    SUM(amount_column) as total_amount,
    AVG(amount_column) as avg_amount
FROM joined_data
GROUP BY 
    SUBSTR(date_column, 1, 5),
    status_column,
    account_type
ORDER BY year_month DESC, status_column, account_type;

SET TIMING OFF

-- 記錄結果
INSERT INTO test_results (test_id, test_description, cost, cardinality, bytes_processed)
SELECT 'TEST_4', '完整優化查詢', cost, cardinality, bytes
FROM plan_table 
WHERE statement_id = 'TEST_4_COMPLETE' AND id = 0;

-- =====================================================
-- 測試結果總結
-- =====================================================

PROMPT ========================================
PROMPT 測試結果總結
PROMPT ========================================

-- 顯示所有測試結果的比較
SELECT 
    test_id,
    test_description,
    cost,
    cardinality,
    ROUND(bytes_processed/1024/1024, 2) as mb_processed,
    CASE 
        WHEN LAG(cost) OVER (ORDER BY test_id) IS NOT NULL THEN
            ROUND((cost - LAG(cost) OVER (ORDER BY test_id)) / LAG(cost) OVER (ORDER BY test_id) * 100, 2)
        ELSE NULL
    END as cost_change_pct,
    test_timestamp
FROM test_results 
ORDER BY test_id;

-- 顯示最佳優化效果
WITH cost_comparison AS (
    SELECT 
        test_id,
        test_description,
        cost,
        FIRST_VALUE(cost) OVER (ORDER BY test_id ROWS UNBOUNDED PRECEDING) as baseline_cost
    FROM test_results
)
SELECT 
    test_id,
    test_description,
    cost,
    baseline_cost,
    ROUND((baseline_cost - cost) / baseline_cost * 100, 2) as improvement_pct
FROM cost_comparison
WHERE cost < baseline_cost
ORDER BY improvement_pct DESC;

-- 清理測試資料 (可選)
-- DROP TABLE test_results;
-- DELETE FROM plan_table WHERE statement_id LIKE 'TEST_%';

PROMPT ========================================
PROMPT 測試完成！請檢查上述結果選擇最佳的優化策略
PROMPT ========================================
