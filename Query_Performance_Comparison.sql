-- =====================================================
-- 查詢效能比較測試腳本
-- 比較原始查詢與優化查詢的效能差異
-- =====================================================

-- 設定測試環境
SET TIMING ON
SET AUTOTRACE ON STATISTICS
SET LINESIZE 200
SET PAGESIZE 1000

-- 清除執行計畫表
DELETE FROM plan_table WHERE statement_id LIKE 'PERF_TEST_%';

-- 建立效能測試記錄表
CREATE TABLE query_performance_test (
    test_name VARCHAR2(50),
    execution_time NUMBER,
    logical_reads NUMBER,
    physical_reads NUMBER,
    rows_returned NUMBER,
    cost NUMBER,
    test_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

PROMPT ========================================
PROMPT 開始效能測試比較
PROMPT ========================================

-- =====================================================
-- 測試 1：原始查詢的執行計畫分析
-- =====================================================

PROMPT 測試 1：分析原始查詢的執行計畫
EXPLAIN PLAN SET STATEMENT_ID = 'PERF_TEST_ORIGINAL' FOR
SELECT ABYYMM, ABVENN, ABCSTN,
       TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2),
               'yyyymmdd') ABDATE,
       ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
       NULL AASTOP, CUST_NO, NULL, NULL, NULL, AAKIND,
       AADISP,
       AAROUT,
       AMT,
       TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
       ABVENN || ABCSTN ABVENN_ABCSTN,
       ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
       ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
  FROM (SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01',
                       'yyyymmdd') ABYYMM, C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
               C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO, NULL,
               NULL, NULL, NULL AAKIND,
               NULL AADISP,
               C.AAROUT,
               ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT, B.BAPRIC,
               B.BARATE,
               B.BARAT1
          FROM (SELECT SUBSTR(ABDATE, 1, 5) ABYYMM, A.ABVENN, C.AAITEM TRADE_TYPE, A.ABCSTN, A.ABDATE, C.AADATE,
                       C.AASTOP, C.AAROUT, A.ABITNO, DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2, A.ABOQTY,
                       A.ABTXAT, A.ABAMTE, A.ABRQTY, A.ABRTXA, A.ABRAMT, A.ABPQTY, A.ABORSD,
                       DECODE(A.ABTYPE, 'V', 'VM', ' O') ABTYPE, A.ABVENN || A.ABCSTN CUST_NO,
                       (ABOQTY + ABPQTY - ABRQTY) QTY
                  FROM ASAB A, SBAI B, ASAA C
                 WHERE A.ABITNO = B.AIITNO (+) AND A.ABVENN = C.AAVENN AND A.ABCSTN = C.AACSTN AND C.AAITEM IS NOT NULL
                   AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4),
                               'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
                 UNION ALL
                SELECT SUBSTR(FADATE, 1, 5) ABYYMM, FAVENN, HCITEM TRADE_TYPE, FACSTN, FADATE, HCCRDT, HCSTDT, HCROUT,
                       NULL FAITNO, FAITNO FAITNO2, FASQTY, FAAMTE, 0, 0, 0, 0, 0, NULL, NULL, FAVENN || FACSTN CUST_NO,
                       FASQTY
                  FROM ASFA A, ASHC C
                 WHERE FAVENN = HCVENN AND FACSTN = HCCSTN AND FAROUT = HCROUT AND TO_DATE(
                         TO_CHAR(TO_NUMBER(SUBSTR(FADATE, 1, 3)) + 1911) || SUBSTR(FADATE, 4, 4),
                         'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE
                 UNION ALL
                SELECT SUBSTR(FFDATE, 1, 5) ABYYMM, FFVENN, HCITEM TRADE_TYPE, FFVENN FFCSTN, FFDATE, HCCRDT, HCSTDT,
                       HCROUT, NULL FFITNO, FFITNO FAITNO2, FFOQTY * 12,
                       0, FFAMTE FFAMTE, 0, 0, 0, 0, NULL, 'EX', FFVENN CUST_NO, FFOQTY * 12
                  FROM ASFF A, ASHC C
                 WHERE FFVENN = HCVENN AND TO_DATE(
                         TO_CHAR(TO_NUMBER(SUBSTR(FFDATE, 1, 3)) + 1911) || SUBSTR(FFDATE, 4, 4),
                         'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE) C,
               (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1 FROM BTBA) B
         WHERE C.ABYYMM = B.BAYYMM AND C.ABITNO2 = B.BAITNO AND TO_DATE(
                 TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 4),
                 'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE);

-- 顯示原始查詢的執行計畫
PROMPT 原始查詢執行計畫：
SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY('PLAN_TABLE', 'PERF_TEST_ORIGINAL', 'ALL'));

-- 記錄原始查詢的成本
INSERT INTO query_performance_test (test_name, cost)
SELECT 'ORIGINAL_QUERY', cost
FROM plan_table 
WHERE statement_id = 'PERF_TEST_ORIGINAL' AND id = 0;

-- =====================================================
-- 測試 2：優化查詢的執行計畫分析
-- =====================================================

PROMPT 測試 2：分析優化查詢的執行計畫
EXPLAIN PLAN SET STATEMENT_ID = 'PERF_TEST_OPTIMIZED' FOR
WITH 
date_boundaries AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36) as start_date,
        SYSDATE as end_date,
        LPAD(TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36), 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36), 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
),
filtered_btba AS (
    SELECT 
        BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1
    FROM BTBA b, date_boundaries db
    WHERE b.BAYYMM >= SUBSTR(db.start_roc_date, 1, 5) || '01'
      AND b.BAPRIC > 0
      AND b.BARATE > 0
),
union_branch_1 AS (
    SELECT 
        SUBSTR(a.ABDATE, 1, 5) as ABYYMM, a.ABVENN, c.AAITEM as TRADE_TYPE, a.ABCSTN, a.ABDATE,
        c.AADATE, c.AASTOP, c.AAROUT, a.ABITNO, COALESCE(b.AIITNO2, a.ABITNO) as ABITNO2,
        a.ABOQTY, a.ABTXAT, a.ABAMTE, a.ABRQTY, a.ABRTXA, a.ABRAMT, a.ABPQTY, a.ABORSD,
        CASE a.ABTYPE WHEN 'V' THEN 'VM' ELSE 'O' END as ABTYPE,
        a.ABVENN || a.ABCSTN as CUST_NO, (a.ABOQTY + a.ABPQTY - a.ABRQTY) as QTY
    FROM ASAB a, date_boundaries db
    LEFT JOIN SBAI b ON a.ABITNO = b.AIITNO
    INNER JOIN ASAA c ON a.ABVENN = c.AAVENN AND a.ABCSTN = c.AACSTN
    WHERE c.AAITEM IS NOT NULL
      AND a.ABDATE BETWEEN db.start_roc_date AND db.end_roc_date
),
union_branch_2 AS (
    SELECT 
        SUBSTR(a.FADATE, 1, 5) as ABYYMM, a.FAVENN as ABVENN, c.HCITEM as TRADE_TYPE,
        a.FACSTN as ABCSTN, a.FADATE as ABDATE, c.HCCRDT as AADATE, c.HCSTDT as AASTOP,
        c.HCROUT as AAROUT, NULL as ABITNO, a.FAITNO as ABITNO2, a.FASQTY as ABOQTY,
        a.FAAMTE as ABTXAT, 0 as ABAMTE, 0 as ABRQTY, 0 as ABRTXA, 0 as ABRAMT,
        0 as ABPQTY, 0 as ABORSD, NULL as ABTYPE, a.FAVENN || a.FACSTN as CUST_NO,
        a.FASQTY as QTY
    FROM ASFA a, date_boundaries db
    INNER JOIN ASHC c ON a.FAVENN = c.HCVENN AND a.FACSTN = c.HCCSTN AND a.FAROUT = c.HCROUT
    WHERE a.FADATE BETWEEN db.start_roc_date AND db.end_roc_date
),
union_branch_3 AS (
    SELECT 
        SUBSTR(a.FFDATE, 1, 5) as ABYYMM, a.FFVENN as ABVENN, c.HCITEM as TRADE_TYPE,
        a.FFVENN as ABCSTN, a.FFDATE as ABDATE, c.HCCRDT as AADATE, c.HCSTDT as AASTOP,
        c.HCROUT as AAROUT, NULL as ABITNO, a.FFITNO as ABITNO2, a.FFOQTY * 12 as ABOQTY,
        0 as ABTXAT, a.FFAMTE as ABAMTE, 0 as ABRQTY, 0 as ABRTXA, 0 as ABRAMT,
        0 as ABPQTY, 0 as ABORSD, 'EX' as ABTYPE, a.FFVENN as CUST_NO, a.FFOQTY * 12 as QTY
    FROM ASFF a, date_boundaries db
    INNER JOIN ASHC c ON a.FFVENN = c.HCVENN
    WHERE a.FFDATE BETWEEN db.start_roc_date AND db.end_roc_date
),
combined_data AS (
    SELECT * FROM union_branch_1
    UNION ALL
    SELECT * FROM union_branch_2  
    UNION ALL
    SELECT * FROM union_branch_3
),
final_calculation AS (
    SELECT 
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd') as ABYYMM,
        c.ABVENN, c.ABCSTN,
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABDATE, 1, 3)) + 1911) || SUBSTR(c.ABDATE, 4, 2) || SUBSTR(c.ABDATE, 6, 2), 'yyyymmdd') as ABDATE,
        c.ABITNO, c.ABOQTY, c.ABTXAT, c.ABAMTE, c.ABRQTY, c.ABRTXA, c.ABRAMT, c.ABPQTY, c.ABORSD,
        c.TRADE_TYPE, NULL as AASTOP, c.CUST_NO, NULL as col1, NULL as col2, NULL as col3,
        NULL as AAKIND, NULL as AADISP, c.AAROUT,
        ROUND(ROUND(c.QTY * b.BAPRIC / b.BARATE, 0) * (1 - b.BARAT1), 0) as AMT,
        TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd'), 'YYYYMM') || c.ABITNO as YYMM_ABITNO,
        c.ABVENN || c.ABCSTN as ABVENN_ABCSTN,
        c.ABVENN || c.ABCSTN || c.TRADE_TYPE as ABVENN_ABCSTN_TYPE,
        c.ABVENN || c.ABCSTN || SUBSTR(c.AAROUT, 1, 1) as ABVENN_ABCSTN_LARGE
    FROM combined_data c
    INNER JOIN filtered_btba b ON c.ABYYMM = b.BAYYMM AND c.ABITNO2 = b.BAITNO
    WHERE c.QTY > 0 AND b.BAPRIC > 0
)
SELECT 
    ABYYMM, ABVENN, ABCSTN, ABDATE, ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT,
    ABPQTY, ABORSD, TRADE_TYPE, AASTOP, CUST_NO, col1, col2, col3, AAKIND, AADISP, AAROUT,
    AMT, YYMM_ABITNO, ABVENN_ABCSTN, ABVENN_ABCSTN_TYPE, ABVENN_ABCSTN_LARGE
FROM final_calculation
ORDER BY ABYYMM DESC, ABVENN, ABCSTN, ABITNO;

-- 顯示優化查詢的執行計畫
PROMPT 優化查詢執行計畫：
SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY('PLAN_TABLE', 'PERF_TEST_OPTIMIZED', 'ALL'));

-- 記錄優化查詢的成本
INSERT INTO query_performance_test (test_name, cost)
SELECT 'OPTIMIZED_QUERY', cost
FROM plan_table 
WHERE statement_id = 'PERF_TEST_OPTIMIZED' AND id = 0;

-- =====================================================
-- 測試 3：實際執行時間比較 (小樣本測試)
-- =====================================================

PROMPT 測試 3：實際執行時間比較 (限制結果數量以加快測試)

-- 測試原始查詢 (限制結果)
PROMPT 執行原始查詢 (前1000筆)...
SET TIMING ON

SELECT * FROM (
    SELECT ABYYMM, ABVENN, ABCSTN,
           TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABDATE, 1, 3)) + 1911) || SUBSTR(ABDATE, 4, 2) || SUBSTR(ABDATE, 6, 2),
                   'yyyymmdd') ABDATE,
           ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT, ABPQTY, ABORSD, TRADE_TYPE,
           NULL AASTOP, CUST_NO, NULL, NULL, NULL, AAKIND, AADISP, AAROUT, AMT,
           TO_CHAR(ABYYMM, 'YYYYMM') || ABITNO YYMM_ABITNO,
           ABVENN || ABCSTN ABVENN_ABCSTN,
           ABVENN || ABCSTN || TRADE_TYPE ABVENN_ABCSTN_TYPE,
           ABVENN || ABCSTN || SUBSTR(AAROUT, 1, 1) ABVENN_ABCSTN_LARGE
      FROM (SELECT TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ABYYMM, 1, 3)) + 1911) || SUBSTR(ABYYMM, 4, 2) || '01',
                           'yyyymmdd') ABYYMM, C.ABVENN, C.ABCSTN, C.ABDATE, C.ABITNO2 ABITNO, C.ABOQTY, C.ABTXAT, C.ABAMTE,
                   C.ABRQTY, C.ABRTXA, C.ABRAMT, C.ABPQTY, C.ABORSD, C.TRADE_TYPE, C.AADATE, C.AASTOP, C.CUST_NO, NULL,
                   NULL, NULL, NULL AAKIND, NULL AADISP, C.AAROUT,
                   ROUND((ROUND((QTY * BAPRIC) / BARATE, 0) * (1 - BARAT1)), 0) AMT, B.BAPRIC, B.BARATE, B.BARAT1
              FROM (SELECT SUBSTR(ABDATE, 1, 5) ABYYMM, A.ABVENN, C.AAITEM TRADE_TYPE, A.ABCSTN, A.ABDATE, C.AADATE,
                           C.AASTOP, C.AAROUT, A.ABITNO, DECODE(A.ABITNO, B.AIITNO, B.AIITNO2, A.ABITNO) ABITNO2, A.ABOQTY,
                           A.ABTXAT, A.ABAMTE, A.ABRQTY, A.ABRTXA, A.ABRAMT, A.ABPQTY, A.ABORSD,
                           DECODE(A.ABTYPE, 'V', 'VM', ' O') ABTYPE, A.ABVENN || A.ABCSTN CUST_NO,
                           (ABOQTY + ABPQTY - ABRQTY) QTY
                      FROM ASAB A, SBAI B, ASAA C
                     WHERE A.ABITNO = B.AIITNO (+) AND A.ABVENN = C.AAVENN AND A.ABCSTN = C.AACSTN AND C.AAITEM IS NOT NULL
                       AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(A.ABDATE, 1, 3)) + 1911) || SUBSTR(A.ABDATE, 4, 4),
                                   'yyyymmdd') BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -12) AND SYSDATE
                       AND ROWNUM <= 500) C,
                   (SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1 FROM BTBA) B
             WHERE C.ABYYMM = B.BAYYMM AND C.ABITNO2 = B.BAITNO)
) WHERE ROWNUM <= 1000;

SET TIMING OFF

-- 測試優化查詢 (限制結果)
PROMPT 執行優化查詢 (前1000筆)...
SET TIMING ON

WITH 
date_boundaries AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -12) as start_date,  -- 縮短測試範圍
        SYSDATE as end_date,
        LPAD(TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -12), 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -12), 'MMDD') as start_roc_date,
        LPAD(TO_CHAR(SYSDATE, 'YYYY') - 1911, 3, '0') || 
        TO_CHAR(SYSDATE, 'MMDD') as end_roc_date
    FROM dual
),
filtered_btba AS (
    SELECT BAYYMM, BAITNO, BAPRIC, BARATE, BARAT1
    FROM BTBA b, date_boundaries db
    WHERE b.BAYYMM >= SUBSTR(db.start_roc_date, 1, 5) || '01'
      AND b.BAPRIC > 0 AND b.BARATE > 0
),
union_branch_1 AS (
    SELECT /*+ FIRST_ROWS(500) */
        SUBSTR(a.ABDATE, 1, 5) as ABYYMM, a.ABVENN, c.AAITEM as TRADE_TYPE, a.ABCSTN, a.ABDATE,
        c.AADATE, c.AASTOP, c.AAROUT, a.ABITNO, COALESCE(b.AIITNO2, a.ABITNO) as ABITNO2,
        a.ABOQTY, a.ABTXAT, a.ABAMTE, a.ABRQTY, a.ABRTXA, a.ABRAMT, a.ABPQTY, a.ABORSD,
        CASE a.ABTYPE WHEN 'V' THEN 'VM' ELSE 'O' END as ABTYPE,
        a.ABVENN || a.ABCSTN as CUST_NO, (a.ABOQTY + a.ABPQTY - a.ABRQTY) as QTY
    FROM ASAB a, date_boundaries db
    LEFT JOIN SBAI b ON a.ABITNO = b.AIITNO
    INNER JOIN ASAA c ON a.ABVENN = c.AAVENN AND a.ABCSTN = c.AACSTN
    WHERE c.AAITEM IS NOT NULL
      AND a.ABDATE BETWEEN db.start_roc_date AND db.end_roc_date
      AND ROWNUM <= 500
),
final_calculation AS (
    SELECT 
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd') as ABYYMM,
        c.ABVENN, c.ABCSTN,
        TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABDATE, 1, 3)) + 1911) || SUBSTR(c.ABDATE, 4, 2) || SUBSTR(c.ABDATE, 6, 2), 'yyyymmdd') as ABDATE,
        c.ABITNO, c.ABOQTY, c.ABTXAT, c.ABAMTE, c.ABRQTY, c.ABRTXA, c.ABRAMT, c.ABPQTY, c.ABORSD,
        c.TRADE_TYPE, NULL as AASTOP, c.CUST_NO, NULL as col1, NULL as col2, NULL as col3,
        NULL as AAKIND, NULL as AADISP, c.AAROUT,
        ROUND(ROUND(c.QTY * b.BAPRIC / b.BARATE, 0) * (1 - b.BARAT1), 0) as AMT,
        TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(c.ABYYMM, 1, 3)) + 1911) || SUBSTR(c.ABYYMM, 4, 2) || '01', 'yyyymmdd'), 'YYYYMM') || c.ABITNO as YYMM_ABITNO,
        c.ABVENN || c.ABCSTN as ABVENN_ABCSTN,
        c.ABVENN || c.ABCSTN || c.TRADE_TYPE as ABVENN_ABCSTN_TYPE,
        c.ABVENN || c.ABCSTN || SUBSTR(c.AAROUT, 1, 1) as ABVENN_ABCSTN_LARGE
    FROM union_branch_1 c
    INNER JOIN filtered_btba b ON c.ABYYMM = b.BAYYMM AND c.ABITNO2 = b.BAITNO
    WHERE c.QTY > 0 AND b.BAPRIC > 0
)
SELECT * FROM (
    SELECT 
        ABYYMM, ABVENN, ABCSTN, ABDATE, ABITNO, ABOQTY, ABTXAT, ABAMTE, ABRQTY, ABRTXA, ABRAMT,
        ABPQTY, ABORSD, TRADE_TYPE, AASTOP, CUST_NO, col1, col2, col3, AAKIND, AADISP, AAROUT,
        AMT, YYMM_ABITNO, ABVENN_ABCSTN, ABVENN_ABCSTN_TYPE, ABVENN_ABCSTN_LARGE
    FROM final_calculation
    ORDER BY ABYYMM DESC, ABVENN, ABCSTN, ABITNO
) WHERE ROWNUM <= 1000;

SET TIMING OFF

-- =====================================================
-- 測試結果分析
-- =====================================================

PROMPT ========================================
PROMPT 效能測試結果分析
PROMPT ========================================

-- 比較執行計畫成本
SELECT 
    test_name,
    cost,
    CASE 
        WHEN LAG(cost) OVER (ORDER BY test_name) IS NOT NULL THEN
            ROUND((LAG(cost) OVER (ORDER BY test_name) - cost) / LAG(cost) OVER (ORDER BY test_name) * 100, 2)
        ELSE NULL
    END as cost_improvement_pct,
    test_timestamp
FROM query_performance_test
ORDER BY test_name;

-- 顯示詳細的執行計畫比較
PROMPT 執行計畫成本比較：
SELECT 
    p1.operation || ' ' || p1.options as operation,
    p1.object_name,
    p1.cost as original_cost,
    p2.cost as optimized_cost,
    ROUND((p1.cost - p2.cost) / NULLIF(p1.cost, 0) * 100, 2) as cost_reduction_pct
FROM plan_table p1
FULL OUTER JOIN plan_table p2 ON p1.id = p2.id
WHERE p1.statement_id = 'PERF_TEST_ORIGINAL'
  AND p2.statement_id = 'PERF_TEST_OPTIMIZED'
  AND (p1.cost > 0 OR p2.cost > 0)
ORDER BY GREATEST(NVL(p1.cost, 0), NVL(p2.cost, 0)) DESC;

-- 清理測試資料
-- DROP TABLE query_performance_test;

SET AUTOTRACE OFF
SET TIMING OFF

PROMPT ========================================
PROMPT 效能測試完成！
PROMPT 請檢查上述結果以評估優化效果
PROMPT ========================================
